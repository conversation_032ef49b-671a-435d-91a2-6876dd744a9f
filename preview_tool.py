#!/usr/bin/env python3
"""
Seed Classification Preview Tool
===============================

A tool to preview segmentation results before running full batch processing.
This helps you tune parameters and verify the segmentation quality.

Usage:
    python preview_tool.py input_directory [--config config.json] [--save-preview]
"""

import os
import cv2
import numpy as np
import argparse
import json
from pathlib import Path
import matplotlib.pyplot as plt
from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig


def create_preview_grid(images_and_results, max_images=6):
    """Create a grid showing original images and segmentation results."""
    n_images = min(len(images_and_results), max_images)
    
    if n_images == 0:
        print("No images to preview")
        return None
    
    # Calculate grid dimensions
    cols = min(3, n_images)
    rows = (n_images + cols - 1) // cols
    
    fig, axes = plt.subplots(rows * 2, cols, figsize=(15, 5 * rows))
    if rows == 1 and cols == 1:
        axes = np.array([[axes[0]], [axes[1]]])
    elif rows == 1:
        axes = axes.reshape(2, cols)
    elif cols == 1:
        axes = axes.reshape(rows * 2, 1)
    
    for i in range(n_images):
        row = (i // cols) * 2
        col = i % cols
        
        image, contours, filename = images_and_results[i]
        
        # Original image
        if len(image.shape) == 3:
            axes[row, col].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            axes[row, col].imshow(image, cmap='gray')
        axes[row, col].set_title(f'Original: {filename}')
        axes[row, col].axis('off')
        
        # Segmentation result
        result_image = image.copy()
        if len(result_image.shape) == 3:
            cv2.drawContours(result_image, contours, -1, (0, 255, 0), 2)
            # Draw bounding rectangles
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 1)
            axes[row + 1, col].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        else:
            # For grayscale, create a colored overlay
            result_image = cv2.cvtColor(result_image, cv2.COLOR_GRAY2BGR)
            cv2.drawContours(result_image, contours, -1, (0, 255, 0), 2)
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 1)
            axes[row + 1, col].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        
        axes[row + 1, col].set_title(f'Detected: {len(contours)} seeds')
        axes[row + 1, col].axis('off')
    
    # Hide unused subplots
    for i in range(n_images, rows * cols):
        row = (i // cols) * 2
        col = i % cols
        axes[row, col].axis('off')
        axes[row + 1, col].axis('off')
    
    plt.tight_layout()
    return fig


def main():
    parser = argparse.ArgumentParser(description='Seed Classification Preview Tool')
    parser.add_argument('input_dir', help='Input directory containing seed images')
    parser.add_argument('--config', help='Path to configuration JSON file')
    parser.add_argument('--save-preview', action='store_true', help='Save preview images to file')
    parser.add_argument('--max-images', type=int, default=6, help='Maximum number of images to preview')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config_data = json.load(f)
        seg_config = SegmentationConfig(**config_data.get('segmentation', {}))
        proc_config_data = config_data.get('processing', {})
    else:
        seg_config = SegmentationConfig()
        proc_config_data = {}
    
    # Set up processing config for preview
    proc_config_data.update({
        'input_directory': args.input_dir,
        'output_directory': './preview_output',
        'preview_mode': True,
        'max_preview_images': args.max_images,
        'save_debug_images': False
    })
    
    proc_config = ProcessingConfig(**proc_config_data)
    
    # Create processor
    processor = SeedProcessor(seg_config, proc_config)
    
    print("Loading and processing preview images...")
    
    # Get image files
    image_files = processor.get_image_files()
    if not image_files:
        print("No image files found in input directory")
        return
    
    # Limit to max images
    image_files = image_files[:args.max_images]
    
    # Process images for preview
    images_and_results = []
    for image_path in image_files:
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            filename = os.path.basename(image_path)
            print(f"Processing {filename}...")
            
            # Segment seeds
            contours, _ = processor.segment_seeds(image)
            
            images_and_results.append((image, contours, filename))
            
            # Print results
            species_id = processor.extract_species_from_filename(filename)
            print(f"  Species ID: {species_id}")
            print(f"  Seeds detected: {len(contours)}")
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
    
    if not images_and_results:
        print("No images could be processed")
        return
    
    # Create preview
    print("\nCreating preview...")
    fig = create_preview_grid(images_and_results, args.max_images)
    
    if fig is None:
        return
    
    # Save or show preview
    if args.save_preview:
        preview_path = Path('./seed_preview.png')
        fig.savefig(preview_path, dpi=150, bbox_inches='tight')
        print(f"Preview saved to: {preview_path}")
    else:
        print("Showing preview... Close the window to continue.")
        plt.show()
    
    # Print summary
    total_seeds = sum(len(contours) for _, contours, _ in images_and_results)
    print(f"\nPreview Summary:")
    print(f"Images processed: {len(images_and_results)}")
    print(f"Total seeds detected: {total_seeds}")
    print(f"Average seeds per image: {total_seeds / len(images_and_results):.1f}")
    
    # Print configuration tips
    print(f"\nCurrent Configuration:")
    print(f"  Threshold method: {seg_config.threshold_method}")
    print(f"  Min contour area: {seg_config.min_contour_area}")
    print(f"  Max contour area: {seg_config.max_contour_area}")
    print(f"  Min seed size: {seg_config.min_seed_width}x{seg_config.min_seed_height}")
    print(f"  Max seed size: {seg_config.max_seed_width}x{seg_config.max_seed_height}")
    
    print(f"\nTips for parameter tuning:")
    print(f"  - If too many small objects detected: increase min_contour_area")
    print(f"  - If seeds are missed: decrease min_contour_area or adjust threshold")
    print(f"  - If background noise detected: adjust threshold parameters")
    print(f"  - Use config_template.json as starting point for custom configuration")


if __name__ == "__main__":
    main()
