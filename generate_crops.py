#!/usr/bin/env python3
"""
生成实际的种子裁剪图像
Generate actual seed crop images from CVH dataset
"""

import cv2
import numpy as np
import os
import re
from pathlib import Path
import json

def process_and_crop_seeds():
    """处理图像并生成实际的种子裁剪图像"""
    print("🌱 开始生成种子裁剪图像...")
    print("🌱 Starting seed crop generation...")
    
    # 输入和输出路径
    input_dir = Path("CVH-seed-pic")
    output_dir = Path("output")
    
    if not input_dir.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        print(f"❌ Input directory not found: {input_dir}")
        return False
    
    # 创建输出目录
    crops_dir = output_dir / "crops"
    crops_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取前3个JPG文件进行处理
    jpg_files = list(input_dir.glob("*.jpg"))[:3]
    if not jpg_files:
        print("❌ 未找到JPG文件")
        print("❌ No JPG files found")
        return False
    
    print(f"✓ 找到 {len(jpg_files)} 个图像文件")
    print(f"✓ Found {len(jpg_files)} image files")
    
    total_crops = 0
    species_class_map = {}
    
    for i, img_path in enumerate(jpg_files):
        print(f"\n📸 处理图像 {i+1}/3: {img_path.name}")
        print(f"📸 Processing image {i+1}/3: {img_path.name}")
        
        try:
            # 加载图像
            image = cv2.imread(str(img_path))
            if image is None:
                print("  ❌ 无法加载图像")
                print("  ❌ Failed to load image")
                continue
            
            print(f"  ✓ 图像尺寸: {image.shape[1]}x{image.shape[0]}")
            print(f"  ✓ Image size: {image.shape[1]}x{image.shape[0]}")
            
            # 提取物种ID
            match = re.search(r'S(\d+)-', img_path.name)
            species_id = match.group(1) if match else f"unknown_{i}"
            
            if species_id not in species_class_map:
                species_class_map[species_id] = len(species_class_map)
            
            print(f"  🏷️ 物种ID: {species_id}")
            print(f"  🏷️ Species ID: {species_id}")
            
            # 图像预处理
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 1.0)
            
            # 自适应阈值
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel, iterations=1)
            
            # 查找轮廓
            contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            good_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 300 < area < 50000:
                    x, y, w, h = cv2.boundingRect(contour)
                    if (15 < w < 500 and 15 < h < 500 and 
                        max(w, h) / min(w, h) < 5.0 and
                        area / (w * h) > 0.3):
                        good_contours.append(contour)
            
            print(f"  ✓ 检测到 {len(good_contours)} 个种子")
            print(f"  ✓ Detected {len(good_contours)} seeds")
            
            if len(good_contours) == 0:
                print("  ⚠️ 未检测到种子")
                print("  ⚠️ No seeds detected")
                continue
            
            # 创建物种目录
            species_dir = crops_dir / f"species_{species_id}"
            species_dir.mkdir(parents=True, exist_ok=True)
            
            # 提取并保存裁剪图像
            base_name = img_path.stem
            crops_saved = 0
            
            for j, contour in enumerate(good_contours):
                x, y, w, h = cv2.boundingRect(contour)
                
                # 添加边距
                pad = 10
                x1 = max(0, x - pad)
                y1 = max(0, y - pad)
                x2 = min(image.shape[1], x + w + pad)
                y2 = min(image.shape[0], y + h + pad)
                
                # 提取裁剪区域
                crop = image[y1:y2, x1:x2]
                
                # 保存裁剪图像
                crop_name = f"{base_name}_seed_{j:03d}.jpg"
                crop_path = species_dir / crop_name
                
                success = cv2.imwrite(str(crop_path), crop)
                if success:
                    crops_saved += 1
                    crop_h, crop_w = crop.shape[:2]
                    print(f"    ✓ 保存: {crop_name} ({crop_w}x{crop_h})")
                    print(f"    ✓ Saved: {crop_name} ({crop_w}x{crop_h})")
                else:
                    print(f"    ❌ 保存失败: {crop_name}")
                    print(f"    ❌ Failed to save: {crop_name}")
            
            total_crops += crops_saved
            print(f"  ✅ 成功保存 {crops_saved} 个裁剪图像")
            print(f"  ✅ Successfully saved {crops_saved} crop images")
            
        except Exception as e:
            print(f"  ❌ 处理错误: {e}")
            print(f"  ❌ Processing error: {e}")
            import traceback
            traceback.print_exc()
    
    # 验证结果
    print(f"\n🔍 验证生成的裁剪图像...")
    print(f"🔍 Verifying generated crop images...")
    
    all_crops = list(crops_dir.rglob("*.jpg"))
    print(f"✓ 总共生成 {len(all_crops)} 个裁剪图像文件")
    print(f"✓ Total {len(all_crops)} crop image files generated")
    
    # 显示每个物种的裁剪图像
    for species_id in species_class_map.keys():
        species_dir = crops_dir / f"species_{species_id}"
        if species_dir.exists():
            species_crops = list(species_dir.glob("*.jpg"))
            print(f"  • 物种 {species_id}: {len(species_crops)} 个裁剪图像")
            print(f"  • Species {species_id}: {len(species_crops)} crop images")
            
            # 显示文件路径
            for crop_file in species_crops:
                rel_path = crop_file.relative_to(Path.cwd())
                print(f"    - {rel_path}")
    
    print(f"\n🎉 裁剪图像生成完成!")
    print(f"🎉 Crop image generation completed!")
    print(f"📁 裁剪图像保存在: {crops_dir.absolute()}")
    print(f"📁 Crop images saved in: {crops_dir.absolute()}")
    
    return len(all_crops) > 0

if __name__ == "__main__":
    success = process_and_crop_seeds()
    if success:
        print("\n✅ 成功生成种子裁剪图像!")
        print("✅ Successfully generated seed crop images!")
    else:
        print("\n❌ 裁剪图像生成失败!")
        print("❌ Failed to generate crop images!")
