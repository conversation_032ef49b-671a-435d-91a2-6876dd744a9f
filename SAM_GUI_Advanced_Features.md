# SAM Seed Segmentation Tool - Advanced Features Guide
# SAM种子分割工具 - 高级功能指南

## 🚀 **New Advanced GUI Features / 新的高级GUI功能**

The SAM Seed Segmentation Tool has been enhanced with powerful new features that provide a complete workflow from segmentation to YOLO training and detection.

SAM种子分割工具已增强了强大的新功能，提供从分割到YOLO训练和检测的完整工作流程。

---

## 🌐 **1. Language Toggle System / 语言切换系统**

### **Features / 功能:**
- **Clean Language Switching** - No more bilingual clutter, choose your preferred language
- **Persistent Settings** - Language preference saved across sessions
- **Complete Translation** - All UI elements, tooltips, and messages change language
- **Menu Bar Toggle** - Easy access via Language menu

### **How to Use / 使用方法:**
1. **Access Language Menu** - Click "Language / 语言" in the menu bar
2. **Select Language** - Choose "English" or "中文"
3. **Instant Update** - All text updates immediately
4. **Automatic Save** - Preference saved to configuration file

### **Supported Languages / 支持的语言:**
- **English** - Complete interface in English
- **中文** - 完整的中文界面

---

## 📁 **2. Session-Based Output Organization / 基于会话的输出组织**

### **Features / 功能:**
- **Unique Session IDs** - Timestamp-based folder naming (YYYYMMDD_HHMMSS)
- **Organized Structure** - Separate folders for different output types
- **Session History** - Browse and manage previous processing sessions
- **Automatic Creation** - New session created for each processing run

### **Session Directory Structure / 会话目录结构:**
```
output_directory/
└── session_20241219_143052/
    ├── crops/          # Segmented seed images / 分割的种子图像
    ├── annotations/    # YOLO format annotations / YOLO格式注释
    ├── debug/          # Visualization images / 可视化图像
    ├── logs/           # Processing logs / 处理日志
    └── reports/        # Session statistics / 会话统计
```

### **How to Use / 使用方法:**
1. **Automatic Session Creation** - New session created when processing starts
2. **Session ID Display** - Current session shown in File Settings tab
3. **Manual Session Creation** - Click "New Session" button
4. **Session Management** - Access previous sessions in YOLO Training tab

---

## 🖼️ **3. Enhanced Results Preview System / 增强的结果预览系统**

### **Features / 功能:**
- **Multi-Image Viewer** - Browse through all debug visualization images
- **Navigation Controls** - Previous/Next buttons and keyboard shortcuts
- **Zoom and Pan** - Detailed inspection of segmentation results
- **Image Information** - Filename, detection count, processing statistics
- **Keyboard Navigation** - A/D keys for quick browsing

### **Navigation Controls / 导航控制:**
- **Previous/Next Buttons** - Click to navigate between images
- **Keyboard Shortcuts:**
  - `A` or `Left Arrow` - Previous image
  - `D` or `Right Arrow` - Next image
  - `Up Arrow` - Zoom in
  - `Down Arrow` - Zoom out
- **Zoom Controls** - Zoom In, Zoom Out, Fit to Window buttons

### **How to Use / 使用方法:**
1. **Process Images** - Run SAM processing to generate debug images
2. **Go to Preview Tab** - Click "Results Preview" tab
3. **Click Refresh** - Load latest debug images
4. **Navigate Images** - Use buttons or keyboard shortcuts
5. **Inspect Details** - Zoom in for detailed examination

---

## 🎯 **4. Integrated YOLO Training Module / 集成YOLO训练模块**

### **Features / 功能:**
- **Session-Based Training** - Select specific processing session for training
- **Data Validation** - Automatic validation of image-annotation pairs
- **Multiple YOLO Models** - Support for YOLOv8, YOLOv9, YOLOv10, YOLOv11
- **Training Configuration** - Customizable epochs, batch size, learning rate
- **Real-Time Progress** - Live training progress monitoring
- **Training Logs** - Detailed training output and metrics

### **Supported YOLO Models / 支持的YOLO模型:**
- **YOLOv8 Series:** yolov8n, yolov8s, yolov8m, yolov8l, yolov8x
- **YOLOv9 Series:** yolov9c, yolov9e
- **YOLOv10 Series:** yolov10n, yolov10s, yolov10m, yolov10l, yolov10x
- **YOLOv11 Series:** yolov11n, yolov11s, yolov11m, yolov11l, yolov11x

### **Training Parameters / 训练参数:**
- **Epochs** - Number of training iterations (10-1000)
- **Batch Size** - Training batch size (1-64)
- **Image Size** - Input image resolution (320, 416, 512, 640, 832, 1024)
- **Learning Rate** - Training learning rate (0.001-0.1)
- **Validation Split** - Percentage for validation data (0.1-0.3)

### **How to Use / 使用方法:**
1. **Go to YOLO Training Tab** - Click "YOLO Training" tab
2. **Select Session** - Choose processing session from dropdown
3. **Validate Data** - Click "Validate Data" to check image-annotation pairs
4. **Configure Training** - Set model type and training parameters
5. **Start Training** - Click "Start Training" to begin
6. **Monitor Progress** - Watch progress bar and training logs

---

## 🔍 **5. YOLO Detection and Visualization / YOLO检测和可视化**

### **Features / 功能:**
- **Model Management** - Load and manage trained YOLO models
- **Batch Detection** - Process multiple images efficiently
- **Configurable Thresholds** - Adjust confidence and NMS thresholds
- **Visual Results** - Bounding boxes with class labels and confidence scores
- **Detection Statistics** - Comprehensive analysis of detection results
- **Export Options** - Multiple output formats for results

### **Detection Parameters / 检测参数:**
- **Confidence Threshold** - Minimum confidence for detections (0.1-1.0)
- **NMS Threshold** - Non-maximum suppression threshold (0.1-1.0)
- **Input Directory** - Folder containing images to process
- **Output Directory** - Where to save detection results

### **Results Visualization / 结果可视化:**
- **Bounding Boxes** - Color-coded boxes around detected objects
- **Class Labels** - Species/class names with confidence scores
- **Side-by-Side View** - Original vs. detected comparison
- **Navigation** - Same A/D key navigation as preview system
- **Statistics Panel** - Detection counts, confidence distribution

### **How to Use / 使用方法:**
1. **Go to YOLO Detection Tab** - Click "YOLO Detection" tab
2. **Load Model** - Browse and select trained YOLO model (.pt file)
3. **Set Parameters** - Adjust confidence and NMS thresholds
4. **Select Input** - Choose directory with images to process
5. **Set Output** - Choose where to save detection results
6. **Start Detection** - Click "Start Detection" to begin processing
7. **View Results** - Browse detection results in visualization tab
8. **Check Statistics** - Review detection statistics and performance

---

## ⚙️ **6. Configuration and Persistence / 配置和持久化**

### **Features / 功能:**
- **Automatic Config Save** - Settings saved automatically
- **Language Persistence** - Language choice remembered across sessions
- **Session Preferences** - Session management settings preserved
- **Parameter Memory** - SAM and filtering parameters saved
- **Import/Export Config** - Load and save configuration files

### **Configuration Files / 配置文件:**
- **sam_gui_config.json** - Main configuration file
- **Language definitions** - UI text in multiple languages
- **Preset configurations** - Quick parameter setups
- **YOLO settings** - Training and detection defaults

---

## 🚀 **7. Getting Started with Advanced Features / 高级功能入门**

### **Quick Start Workflow / 快速开始工作流程:**

1. **Launch Advanced GUI / 启动高级GUI**
   ```bash
   python launch_sam_gui.py
   ```

2. **Set Language Preference / 设置语言偏好**
   - Menu Bar → Language / 语言 → Choose your language

3. **Configure SAM Processing / 配置SAM处理**
   - File Settings → Set input/output directories
   - Parameters → Adjust SAM settings or use presets

4. **Process Images / 处理图像**
   - Processing Control → Start Processing
   - Monitor progress and logs

5. **Preview Results / 预览结果**
   - Results Preview → Refresh Preview
   - Navigate through debug images

6. **Train YOLO Model / 训练YOLO模型**
   - YOLO Training → Select session
   - Validate data → Configure training → Start training

7. **Use Trained Model / 使用训练的模型**
   - YOLO Detection → Load model
   - Set parameters → Start detection → View results

---

## 🔧 **8. Advanced Tips and Tricks / 高级技巧**

### **Performance Optimization / 性能优化:**
- Use **Preview Mode** for testing with limited images
- Choose appropriate **YOLO model size** (n=nano, s=small, m=medium, l=large, x=extra-large)
- Adjust **batch size** based on available GPU memory
- Use **GPU acceleration** when available (device="cuda")

### **Quality Improvement / 质量改进:**
- Use **High Precision preset** for best segmentation quality
- Adjust **confidence thresholds** based on your data
- Validate training data before starting YOLO training
- Monitor training logs for overfitting

### **Workflow Efficiency / 工作流程效率:**
- Create **separate sessions** for different datasets
- Use **keyboard shortcuts** for faster navigation
- Save **custom configurations** for different use cases
- Export **detection results** in multiple formats

---

## 📚 **9. Troubleshooting / 故障排除**

### **Common Issues / 常见问题:**

1. **Language not changing / 语言未更改**
   - Check if config file is writable
   - Restart application after language change

2. **Session directory not found / 找不到会话目录**
   - Ensure output directory exists and is writable
   - Check session ID in File Settings tab

3. **YOLO training fails / YOLO训练失败**
   - Validate training data first
   - Check if required packages are installed
   - Monitor system resources (RAM, GPU memory)

4. **Detection results not showing / 检测结果未显示**
   - Verify model file path is correct
   - Check confidence threshold settings
   - Ensure input images are in supported formats

### **Getting Help / 获取帮助:**
- Check processing logs for error messages
- Use "About" dialog for version information
- Review configuration file for settings
- Consult documentation files in project directory

---

**The Advanced SAM GUI provides a complete solution for seed segmentation, from initial processing through YOLO training to final detection and analysis!**

**高级SAM GUI为种子分割提供了完整的解决方案，从初始处理到YOLO训练再到最终检测和分析！** 🌱✨
