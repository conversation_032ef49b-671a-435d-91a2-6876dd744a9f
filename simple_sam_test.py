#!/usr/bin/env python3
"""
简单的SAM测试脚本
Simple SAM test script
"""

import cv2
import numpy as np
from pathlib import Path

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import segment_anything
        print("✅ Segment Anything: 已安装")
        
        from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
        print("✅ SAM组件: 可以导入")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_model_file():
    """测试模型文件"""
    print("\n📁 检查模型文件...")
    
    model_path = "sam_vit_h_4b8939.pth"
    if Path(model_path).exists():
        size_mb = Path(model_path).stat().st_size / (1024**2)
        print(f"✅ 模型文件存在: {model_path} ({size_mb:.1f} MB)")
        return True
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        return False

def test_image_loading():
    """测试图像加载"""
    print("\n🖼️ 测试图像加载...")
    
    test_image = "CVH-seed-pic/S0000012-1.jpg"
    if Path(test_image).exists():
        image = cv2.imread(test_image)
        if image is not None:
            print(f"✅ 图像加载成功: {image.shape}")
            return True, image
        else:
            print("❌ 图像加载失败")
            return False, None
    else:
        print(f"❌ 测试图像不存在: {test_image}")
        return False, None

def test_sam_model_loading():
    """测试SAM模型加载"""
    print("\n🤖 测试SAM模型加载...")
    
    try:
        from segment_anything import sam_model_registry
        
        model_path = "sam_vit_h_4b8939.pth"
        if not Path(model_path).exists():
            print(f"❌ 模型文件不存在: {model_path}")
            return False, None
        
        print("正在加载SAM模型...")
        sam = sam_model_registry["vit_h"](checkpoint=model_path)
        print("✅ SAM模型加载成功!")
        
        return True, sam
    except Exception as e:
        print(f"❌ SAM模型加载失败: {e}")
        return False, None

def test_sam_inference(sam_model, image):
    """测试SAM推理"""
    print("\n🔮 测试SAM推理...")
    
    try:
        from segment_anything import SamAutomaticMaskGenerator
        
        print("创建掩码生成器...")
        mask_generator = SamAutomaticMaskGenerator(
            model=sam_model,
            points_per_side=16,  # 减少点数以加快速度
            pred_iou_thresh=0.88,
            stability_score_thresh=0.95,
            min_mask_region_area=1000,
        )
        
        print("生成掩码...")
        masks = mask_generator.generate(image)
        
        print(f"✅ SAM推理成功! 生成了 {len(masks)} 个掩码")
        return True, masks
        
    except Exception as e:
        print(f"❌ SAM推理失败: {e}")
        return False, []

def create_simple_visualization(image, masks):
    """创建简单的可视化"""
    print("\n🎨 创建可视化...")
    
    try:
        result_image = image.copy()
        
        if len(masks) > 0:
            for i, mask_data in enumerate(masks[:10]):  # 只显示前10个
                mask = mask_data['segmentation']
                
                # 创建随机颜色
                color = np.random.randint(0, 255, 3).tolist()
                
                # 应用颜色掩码
                colored_mask = np.zeros_like(image)
                colored_mask[mask] = color
                
                # 叠加到结果图像
                result_image = cv2.addWeighted(result_image, 0.8, colored_mask, 0.2, 0)
            
            # 添加文本
            cv2.putText(result_image, f"SAM detected: {len(masks)} objects", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        else:
            cv2.putText(result_image, "No objects detected", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        # 保存结果
        output_path = "sam_test_result.jpg"
        cv2.imwrite(output_path, result_image)
        print(f"✅ 可视化结果保存到: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

def main():
    """主函数"""
    print("🌱 SAM种子分割 - 简单测试")
    print("="*40)
    
    # 测试步骤
    steps = [
        ("基本导入", test_basic_imports),
        ("模型文件", test_model_file),
    ]
    
    # 执行基本测试
    for step_name, step_func in steps:
        print(f"\n{'='*10} {step_name} {'='*10}")
        if not step_func():
            print(f"\n❌ {step_name}测试失败")
            return 1
    
    # 测试图像加载
    print(f"\n{'='*10} 图像加载 {'='*10}")
    image_success, image = test_image_loading()
    if not image_success:
        print("\n❌ 图像加载测试失败")
        return 1
    
    # 测试SAM模型加载
    print(f"\n{'='*10} SAM模型加载 {'='*10}")
    model_success, sam_model = test_sam_model_loading()
    if not model_success:
        print("\n❌ SAM模型加载测试失败")
        return 1
    
    # 测试SAM推理
    print(f"\n{'='*10} SAM推理 {'='*10}")
    inference_success, masks = test_sam_inference(sam_model, image)
    if not inference_success:
        print("\n❌ SAM推理测试失败")
        return 1
    
    # 创建可视化
    print(f"\n{'='*10} 可视化 {'='*10}")
    viz_success = create_simple_visualization(image, masks)
    if not viz_success:
        print("\n❌ 可视化测试失败")
        return 1
    
    print("\n🎉 所有测试通过!")
    print("🎉 All tests passed!")
    print(f"\n📊 检测结果: {len(masks)} 个对象")
    print(f"📊 Detection result: {len(masks)} objects")
    print("📁 查看 sam_test_result.jpg 了解检测效果")
    print("📁 Check sam_test_result.jpg for detection results")
    
    return 0

if __name__ == "__main__":
    exit(main())
