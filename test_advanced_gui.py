#!/usr/bin/env python3
"""
Test script for SAM Advanced GUI
Tests basic functionality without requiring SAM model
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ Tkinter modules imported successfully")
    except ImportError as e:
        print(f"❌ Tkinter import error: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✅ PIL/Pillow imported successfully")
    except ImportError as e:
        print(f"❌ PIL/Pillow import error: {e}")
        print("Install with: pip install pillow")
        return False
    
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except ImportError as e:
        print(f"❌ OpenCV import error: {e}")
        print("Install with: pip install opencv-python")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except ImportError as e:
        print(f"❌ NumPy import error: {e}")
        print("Install with: pip install numpy")
        return False
    
    return True

def test_config_file():
    """Test if configuration file exists and is valid"""
    print("\nTesting configuration file...")
    
    config_file = "sam_gui_config.json"
    if not Path(config_file).exists():
        print(f"❌ Configuration file not found: {config_file}")
        return False
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Check for required sections
        required_sections = ['languages', 'tooltips', 'gui_settings']
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing configuration section: {section}")
                return False
        
        print("✅ Configuration file is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Configuration file JSON error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration file error: {e}")
        return False

def test_advanced_gui_import():
    """Test if advanced GUI can be imported"""
    print("\nTesting advanced GUI import...")
    
    try:
        from sam_gui_advanced import SAMGUIAdvanced, LanguageManager, SessionManager
        print("✅ Advanced GUI classes imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Advanced GUI import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Advanced GUI error: {e}")
        return False

def test_language_manager():
    """Test language manager functionality"""
    print("\nTesting language manager...")
    
    try:
        from sam_gui_advanced import LanguageManager
        
        lang_mgr = LanguageManager("sam_gui_config.json")
        
        # Test language switching
        lang_mgr.set_language('en')
        assert lang_mgr.current_language == 'en'
        
        lang_mgr.set_language('zh')
        assert lang_mgr.current_language == 'zh'
        
        # Test text retrieval
        text = lang_mgr.get_text('app_title', 'Default Title')
        assert text is not None
        
        print("✅ Language manager working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Language manager error: {e}")
        return False

def test_session_manager():
    """Test session manager functionality"""
    print("\nTesting session manager...")
    
    try:
        from sam_gui_advanced import SessionManager
        
        # Create temporary test directory
        test_dir = Path("test_output")
        test_dir.mkdir(exist_ok=True)
        
        session_mgr = SessionManager(test_dir)
        
        # Test session creation
        session_id, session_dir = session_mgr.create_new_session()
        assert session_id is not None
        assert session_dir.exists()
        
        # Test session listing
        sessions = session_mgr.get_session_list()
        assert session_id in sessions
        
        print("✅ Session manager working correctly")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager error: {e}")
        return False

def test_gui_creation():
    """Test if GUI can be created without errors"""
    print("\nTesting GUI creation...")
    
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        from sam_gui_advanced import SAMGUIAdvanced
        
        # Create GUI instance
        app = SAMGUIAdvanced(root)
        
        # Test basic functionality
        assert app.lang_mgr is not None
        assert app.session_mgr is not None
        
        # Test language switching
        app.change_language('en')
        assert app.lang_mgr.current_language == 'en'
        
        app.change_language('zh')
        assert app.lang_mgr.current_language == 'zh'
        
        print("✅ GUI creation successful")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI creation error: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("SAM Advanced GUI Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Config File Test", test_config_file),
        ("Advanced GUI Import", test_advanced_gui_import),
        ("Language Manager Test", test_language_manager),
        ("Session Manager Test", test_session_manager),
        ("GUI Creation Test", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 50)
    
    if passed == total:
        print("🎉 All tests passed! Advanced GUI is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--launch":
        # Launch the actual GUI
        print("Launching SAM Advanced GUI...")
        try:
            from sam_gui_advanced import main as gui_main
            gui_main()
        except Exception as e:
            print(f"Failed to launch GUI: {e}")
            return 1
    else:
        # Run tests
        success = run_all_tests()
        
        if success:
            print("\nTo launch the advanced GUI, run:")
            print("python test_advanced_gui.py --launch")
            print("or")
            print("python launch_sam_gui.py")
            return 0
        else:
            return 1

if __name__ == "__main__":
    sys.exit(main())
