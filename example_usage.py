#!/usr/bin/env python3
"""
Example Usage of Seed Classification Tool
=========================================

This script demonstrates different ways to use the seed classification tool.
"""

import os
import json
from pathlib import Path
from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig


def example_basic_usage():
    """Example 1: Basic usage with default settings."""
    print("Example 1: Basic Usage")
    print("-" * 40)
    
    # Define paths
    input_dir = "CVH-seed-pic/CVH-seed-pic"
    output_dir = "./output_basic"
    
    # Create configurations with default settings
    seg_config = SegmentationConfig()
    proc_config = ProcessingConfig(
        input_directory=input_dir,
        output_directory=output_dir,
        preview_mode=True,  # Process only a few images for demo
        max_preview_images=3
    )
    
    # Create processor and run
    processor = SeedProcessor(seg_config, proc_config)
    
    try:
        results = processor.process_batch()
        if results['success']:
            summary = results['summary']
            print(f"✓ Processed {summary['successful_images']} images")
            print(f"✓ Found {summary['total_seeds_extracted']} seeds")
            print(f"✓ Results saved to: {output_dir}")
        else:
            print(f"✗ Processing failed: {results.get('error')}")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print()


def example_custom_configuration():
    """Example 2: Using custom configuration for smaller seeds."""
    print("Example 2: Custom Configuration for Smaller Seeds")
    print("-" * 50)
    
    # Define paths
    input_dir = "CVH-seed-pic/CVH-seed-pic"
    output_dir = "./output_small_seeds"
    
    # Custom configuration for detecting smaller seeds
    seg_config = SegmentationConfig(
        min_contour_area=200,      # Lower minimum area
        min_seed_width=15,         # Smaller minimum width
        min_seed_height=15,        # Smaller minimum height
        adaptive_block_size=9,     # Smaller block size for finer details
        morph_iterations=1         # Less morphological processing
    )
    
    proc_config = ProcessingConfig(
        input_directory=input_dir,
        output_directory=output_dir,
        preview_mode=True,
        max_preview_images=3,
        save_debug_images=True     # Save debug images to see results
    )
    
    # Create processor and run
    processor = SeedProcessor(seg_config, proc_config)
    
    try:
        results = processor.process_batch()
        if results['success']:
            summary = results['summary']
            print(f"✓ Processed {summary['successful_images']} images")
            print(f"✓ Found {summary['total_seeds_extracted']} seeds")
            print(f"✓ Debug images saved to: {output_dir}/debug/")
        else:
            print(f"✗ Processing failed: {results.get('error')}")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print()


def example_with_config_file():
    """Example 3: Using configuration file."""
    print("Example 3: Using Configuration File")
    print("-" * 40)
    
    # Create a custom configuration file
    config_data = {
        "segmentation": {
            "threshold_method": "otsu",
            "min_contour_area": 600,
            "max_contour_area": 40000,
            "min_seed_width": 25,
            "min_seed_height": 25,
            "morph_iterations": 3
        },
        "processing": {
            "preview_mode": True,
            "max_preview_images": 2,
            "save_debug_images": True,
            "species_extraction_pattern": "S(\\d+)-"
        }
    }
    
    # Save configuration file
    config_file = "example_config.json"
    with open(config_file, 'w') as f:
        json.dump(config_data, f, indent=2)
    
    print(f"✓ Created configuration file: {config_file}")
    
    # Load configuration
    with open(config_file, 'r') as f:
        loaded_config = json.load(f)
    
    seg_config = SegmentationConfig(**loaded_config['segmentation'])
    proc_config_data = loaded_config['processing']
    proc_config_data.update({
        'input_directory': "CVH-seed-pic/CVH-seed-pic",
        'output_directory': "./output_config_file"
    })
    proc_config = ProcessingConfig(**proc_config_data)
    
    # Process
    processor = SeedProcessor(seg_config, proc_config)
    
    try:
        results = processor.process_batch()
        if results['success']:
            summary = results['summary']
            print(f"✓ Processed {summary['successful_images']} images")
            print(f"✓ Found {summary['total_seeds_extracted']} seeds")
        else:
            print(f"✗ Processing failed: {results.get('error')}")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print()


def example_analyze_single_image():
    """Example 4: Analyze a single image in detail."""
    print("Example 4: Single Image Analysis")
    print("-" * 35)
    
    # Find first available image
    input_dir = Path("CVH-seed-pic/CVH-seed-pic")
    image_files = list(input_dir.glob("*.jpg"))
    
    if not image_files:
        print("✗ No image files found")
        return
    
    image_path = str(image_files[0])
    print(f"Analyzing: {os.path.basename(image_path)}")
    
    # Create processor
    seg_config = SegmentationConfig()
    proc_config = ProcessingConfig(
        input_directory=str(input_dir),
        output_directory="./output_single",
        save_debug_images=True
    )
    
    processor = SeedProcessor(seg_config, proc_config)
    
    try:
        # Process single image
        result = processor.process_single_image(image_path)
        
        if result['success']:
            print(f"✓ Species ID: {result['species_id']}")
            print(f"✓ Seeds found: {result['seeds_found']}")
            print(f"✓ YOLO annotation preview:")
            print(result['yolo_annotation'][:200] + "..." if len(result['yolo_annotation']) > 200 else result['yolo_annotation'])
        else:
            print(f"✗ Processing failed: {result.get('error')}")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print()


def main():
    """Run all examples."""
    print("Seed Classification Tool - Usage Examples")
    print("=" * 50)
    print()
    
    # Check if input directory exists
    if not os.path.exists("CVH-seed-pic/CVH-seed-pic"):
        print("⚠ Warning: Input directory 'CVH-seed-pic/CVH-seed-pic' not found")
        print("Please update the paths in this script to match your data location.")
        print()
    
    # Run examples
    example_basic_usage()
    example_custom_configuration()
    example_with_config_file()
    example_analyze_single_image()
    
    print("Examples completed!")
    print("\nNext steps:")
    print("1. Use preview_tool.py to visualize segmentation results")
    print("2. Adjust configuration parameters based on your data")
    print("3. Run full processing without preview_mode=True")
    print("4. Check the output directories for results")


if __name__ == "__main__":
    main()
