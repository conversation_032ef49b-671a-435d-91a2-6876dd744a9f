#!/usr/bin/env python3
"""
测试修复后的种子检测
Test fixed seed detection
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig
    import json
    print("✅ 成功导入模块")
    print("✅ Successfully imported modules")
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_single_image_processing():
    """测试单个图像处理"""
    print("🧪 测试修复后的种子检测")
    print("🧪 Testing fixed seed detection")
    print("="*50)
    
    # 测试图像
    test_image = "CVH-seed-pic/S0000012-1.jpg"
    
    if not Path(test_image).exists():
        print(f"❌ 测试图像不存在: {test_image}")
        print(f"❌ Test image not found: {test_image}")
        return False
    
    print(f"📸 测试图像: {test_image}")
    print(f"📸 Test image: {test_image}")
    
    # 加载多背景配置
    config_file = "config_multi_background.json"
    if Path(config_file).exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            seg_config = SegmentationConfig(**config_data['segmentation'])
            print("✅ 使用多背景优化配置")
            print("✅ Using multi-background optimized config")
        except Exception as e:
            print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
            print(f"⚠️ Config file loading failed, using defaults: {e}")
            seg_config = SegmentationConfig()
    else:
        print("⚠️ 使用默认配置")
        print("⚠️ Using default configuration")
        seg_config = SegmentationConfig()
    
    # 处理配置
    proc_config = ProcessingConfig(
        input_directory="CVH-seed-pic",
        output_directory="./test_output_fixed",
        preview_mode=True,
        save_debug_images=True,
        max_preview_images=1
    )
    
    try:
        # 创建处理器
        processor = SeedProcessor(seg_config, proc_config)
        print("✅ 处理器创建成功")
        print("✅ Processor created successfully")
        
        # 处理单个图像
        result = processor.process_single_image(test_image)
        
        print("\n📊 处理结果:")
        print("📊 Processing results:")
        print(f"  文件名: {result['filename']}")
        print(f"  Filename: {result['filename']}")
        print(f"  物种ID: {result['species_id']}")
        print(f"  Species ID: {result['species_id']}")
        print(f"  检测到的种子数: {result['seeds_found']}")
        print(f"  Seeds detected: {result['seeds_found']}")
        print(f"  处理成功: {result['success']}")
        print(f"  Processing success: {result['success']}")
        
        if result['success']:
            print("🎉 种子检测成功!")
            print("🎉 Seed detection successful!")
            
            if result['seeds_found'] > 0:
                print(f"✅ 成功检测到 {result['seeds_found']} 个种子")
                print(f"✅ Successfully detected {result['seeds_found']} seeds")
                
                # 检查调试图像
                if result.get('debug_image_path'):
                    debug_path = result['debug_image_path']
                    if Path(debug_path).exists():
                        print(f"🖼️ 调试图像已保存: {debug_path}")
                        print(f"🖼️ Debug image saved: {debug_path}")
                    else:
                        print("⚠️ 调试图像路径存在但文件未找到")
                        print("⚠️ Debug image path exists but file not found")
                
                # 检查YOLO注释
                if result.get('yolo_annotation'):
                    lines = result['yolo_annotation'].strip().split('\n')
                    print(f"📝 YOLO注释 ({len(lines)} 行):")
                    print(f"📝 YOLO annotation ({len(lines)} lines):")
                    for i, line in enumerate(lines[:3]):  # 显示前3行
                        print(f"    {line}")
                    if len(lines) > 3:
                        print(f"    ... 还有 {len(lines) - 3} 行")
                        print(f"    ... and {len(lines) - 3} more lines")
            else:
                print("⚠️ 检测成功但未找到种子")
                print("⚠️ Detection successful but no seeds found")
        else:
            print("❌ 种子检测失败")
            print("❌ Seed detection failed")
            if 'error' in result:
                print(f"错误信息: {result['error']}")
                print(f"Error message: {result['error']}")
        
        return result['success'] and result['seeds_found'] > 0
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🌱 种子检测修复验证")
    print("🌱 Seed Detection Fix Verification")
    print("="*60)
    
    # 检查必要文件
    required_files = [
        "seed_classifier.py",
        "CVH-seed-pic/S0000012-1.jpg"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 所有必要文件存在")
    print("✅ All required files present")
    
    # 运行测试
    success = test_single_image_processing()
    
    print("\n" + "="*60)
    if success:
        print("🎉 测试成功! 种子检测问题已修复!")
        print("🎉 Test successful! Seed detection issues fixed!")
        print("\n建议下一步:")
        print("Suggested next steps:")
        print("1. 运行完整处理: python seed_classifier.py \"CVH-seed-pic\" \"./output\" --config config_multi_background.json --preview --debug")
        print("2. 检查调试图像确认检测效果")
        print("3. 如果满意，移除 --preview 标志处理完整数据集")
    else:
        print("❌ 测试失败，需要进一步调试")
        print("❌ Test failed, further debugging needed")
    
    return success

if __name__ == "__main__":
    main()
