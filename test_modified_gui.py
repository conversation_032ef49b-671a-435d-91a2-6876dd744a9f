#!/usr/bin/env python3
"""
Test script for the modified SAM GUI with Chinese interface and enhanced YOLO features
"""

import sys
import tkinter as tk

def test_modified_gui():
    """Test the modified GUI"""
    try:
        print("测试修改后的SAM高级GUI...")
        
        # Test import
        from sam_gui_advanced_fixed import SAMGUIAdvancedFixed, ChineseTextManager, SessionManager
        print("✅ 修改后的GUI类导入成功")
        
        # Test Chinese text manager
        text_mgr = ChineseTextManager()
        print(f"✅ 中文文本管理器创建成功")
        print(f"   应用标题: {text_mgr.get_text('app_title')}")
        print(f"   文件设置: {text_mgr.get_text('file_settings')}")
        print(f"   YOLO训练: {text_mgr.get_text('yolo_training')}")
        
        # Test session manager
        session_mgr = SessionManager("test_output")
        print("✅ 会话管理器创建成功")
        
        # Test session creation
        session_id, session_dir = session_mgr.create_new_session()
        print(f"✅ 会话创建成功: {session_id}")
        
        # Test session list
        sessions = session_mgr.get_session_list()
        print(f"✅ 会话列表获取成功: {len(sessions)} 个会话")
        
        # Test GUI creation (without showing)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        app = SAMGUIAdvancedFixed(root)
        print("✅ 修改后的GUI应用程序创建成功")
        
        # Test basic functionality
        app.create_new_session()
        print("✅ 会话创建功能正常")
        
        # Test parameter setting
        app.points_per_side.set(64)
        app.pred_iou_thresh.set(0.9)
        print("✅ 参数设置功能正常")
        
        # Test preset application
        app.apply_preset('high_precision')
        print("✅ 预设配置应用功能正常")
        
        app.log_message("测试消息")
        print("✅ 日志记录功能正常")
        
        root.destroy()
        print("✅ GUI清理成功")
        
        # Cleanup test session
        import shutil
        shutil.rmtree("test_output", ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yolo_features():
    """Test YOLO-specific features"""
    try:
        print("\n测试YOLO功能...")
        
        from sam_gui_advanced_fixed import SAMGUIAdvancedFixed
        
        # Create a minimal GUI for testing
        root = tk.Tk()
        root.withdraw()
        
        app = SAMGUIAdvancedFixed(root)
        
        # Test YOLO training variables
        if hasattr(app, 'yolo_model_var'):
            app.yolo_model_var.set('yolov8n')
            print("✅ YOLO模型选择功能正常")
        
        if hasattr(app, 'epochs_var'):
            app.epochs_var.set(50)
            print("✅ 训练参数设置功能正常")
        
        if hasattr(app, 'confidence_var'):
            app.confidence_var.set(0.5)
            print("✅ 检测参数设置功能正常")
        
        # Test method existence
        methods_to_test = [
            'browse_external_dataset',
            'browse_pretrained_model', 
            'validate_training_data',
            'browse_detection_model',
            'validate_detection_model',
            'log_training',
            'log_detection'
        ]
        
        for method_name in methods_to_test:
            if hasattr(app, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法缺失")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ YOLO功能测试失败: {e}")
        return False

def test_chinese_interface():
    """Test Chinese interface elements"""
    try:
        print("\n测试中文界面...")
        
        from sam_gui_advanced_fixed import ChineseTextManager
        
        text_mgr = ChineseTextManager()
        
        # Test key interface elements
        key_texts = [
            'app_title', 'file_settings', 'parameters', 'processing_control',
            'preview', 'yolo_training', 'yolo_detection', 'sam_parameters',
            'seed_filtering', 'high_precision', 'balanced', 'fast'
        ]
        
        for key in key_texts:
            text = text_mgr.get_text(key)
            if text and text != key:  # Should return Chinese text, not the key
                print(f"✅ {key}: {text}")
            else:
                print(f"❌ {key}: 文本缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 中文界面测试失败: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("修改后的SAM高级GUI测试")
    print("=" * 60)
    
    tests = [
        ("基础GUI功能", test_modified_gui),
        ("YOLO功能", test_yolo_features),
        ("中文界面", test_chinese_interface),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n--- {test_name}测试 ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{len(tests)} 项测试通过")
    print("=" * 60)
    
    if passed == len(tests):
        print("🎉 所有测试通过！修改后的GUI可以使用。")
        print("\n启动修改后的GUI:")
        print("python sam_gui_advanced_fixed.py")
        print("或")
        print("python launch_sam_gui.py")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查上述错误。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
