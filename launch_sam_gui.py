#!/usr/bin/env python3
"""
SAM种子分割工具GUI启动器
SAM Seed Segmentation Tool GUI Launcher
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    # 检查基本依赖
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import torch
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import segment_anything
    except ImportError:
        missing_deps.append("segment-anything")
    
    return missing_deps

def check_files():
    """检查必要文件"""
    required_files = [
        "sam_seed_segmenter.py",
        "sam_gui.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    return missing_files

def show_dependency_help(missing_deps):
    """显示依赖安装帮助"""
    help_text = f"""
缺少以下依赖项 / Missing Dependencies:
{', '.join(missing_deps)}

请运行以下命令安装 / Please run the following commands to install:

pip install {' '.join(missing_deps)}

或者运行自动安装脚本 / Or run the auto-install script:
python install_sam_dependencies.py
    """
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    messagebox.showerror("依赖项缺失 / Missing Dependencies", help_text)
    root.destroy()

def show_file_help(missing_files):
    """显示文件缺失帮助"""
    help_text = f"""
缺少以下必要文件 / Missing Required Files:
{', '.join(missing_files)}

请确保所有文件都在同一目录中 / Please ensure all files are in the same directory.
    """
    
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror("文件缺失 / Missing Files", help_text)
    root.destroy()

def main():
    """主函数"""
    print("🚀 启动SAM种子分割工具GUI...")
    print("🚀 Launching SAM Seed Segmentation Tool GUI...")
    
    # 检查依赖项
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"❌ 缺少依赖项: {', '.join(missing_deps)}")
        print(f"❌ Missing dependencies: {', '.join(missing_deps)}")
        show_dependency_help(missing_deps)
        return 1
    
    # 检查文件
    missing_files = check_files()
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        print(f"❌ Missing files: {', '.join(missing_files)}")
        show_file_help(missing_files)
        return 1
    
    # 检查SAM模型文件
    model_file = "sam_vit_h_4b8939.pth"
    if not Path(model_file).exists():
        print(f"⚠️ SAM模型文件不存在: {model_file}")
        print(f"⚠️ SAM model file not found: {model_file}")
        
        root = tk.Tk()
        root.withdraw()
        
        result = messagebox.askyesno(
            "模型文件缺失 / Model File Missing",
            f"""SAM模型文件不存在: {model_file}

您可以:
1. 下载模型文件到当前目录
2. 在GUI中指定模型文件路径

是否继续启动GUI? / Continue launching GUI?"""
        )
        
        root.destroy()
        
        if not result:
            return 1
    
    try:
        # 启动GUI
        print("✅ 所有检查通过，启动GUI...")
        print("✅ All checks passed, launching GUI...")
        
        from sam_gui import main as gui_main
        gui_main()
        
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        print(f"❌ Failed to launch GUI: {e}")
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败 / Launch Failed", f"启动GUI失败:\n{e}")
        root.destroy()
        
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
