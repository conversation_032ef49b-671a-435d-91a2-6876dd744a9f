#!/usr/bin/env python3
"""
SAM种子分割工具GUI启动器
SAM Seed Segmentation Tool GUI Launcher
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    # 检查基本依赖
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import torch
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import segment_anything
    except ImportError:
        missing_deps.append("segment-anything")
    
    return missing_deps

def check_files():
    """检查必要文件"""
    required_files = [
        "sam_seed_segmenter.py",
        "sam_gui.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    return missing_files

def show_dependency_help(missing_deps):
    """显示依赖安装帮助"""
    help_text = f"""
缺少以下依赖项 / Missing Dependencies:
{', '.join(missing_deps)}

请运行以下命令安装 / Please run the following commands to install:

pip install {' '.join(missing_deps)}

或者运行自动安装脚本 / Or run the auto-install script:
python install_sam_dependencies.py
    """
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    messagebox.showerror("依赖项缺失 / Missing Dependencies", help_text)
    root.destroy()

def show_file_help(missing_files):
    """显示文件缺失帮助"""
    help_text = f"""
缺少以下必要文件 / Missing Required Files:
{', '.join(missing_files)}

请确保所有文件都在同一目录中 / Please ensure all files are in the same directory.
    """
    
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror("文件缺失 / Missing Files", help_text)
    root.destroy()

def main():
    """Main function with better encoding support"""
    # Set UTF-8 encoding for console output
    import sys
    if sys.platform.startswith('win'):
        try:
            # Try to set UTF-8 encoding on Windows
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
        except:
            pass  # Fall back to default encoding

    print("🚀 Launching SAM Seed Segmentation Tool GUI...")
    print("🚀 启动SAM种子分割工具GUI...")

    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"❌ Missing dependencies: {', '.join(missing_deps)}")
        print(f"❌ 缺少依赖项: {', '.join(missing_deps)}")
        show_dependency_help(missing_deps)
        return 1

    # Check files
    missing_files = check_files()
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        show_file_help(missing_files)
        return 1

    # Check SAM model file
    model_file = "sam_vit_h_4b8939.pth"
    if not Path(model_file).exists():
        print(f"⚠️ SAM model file not found: {model_file}")
        print(f"⚠️ SAM模型文件不存在: {model_file}")

        root = tk.Tk()
        root.withdraw()

        result = messagebox.askyesno(
            "Model File Missing / 模型文件缺失",
            f"""SAM model file not found: {model_file}
SAM模型文件不存在: {model_file}

You can:
1. Download the model file to current directory
2. Specify model file path in GUI

Continue launching GUI?
是否继续启动GUI?"""
        )

        root.destroy()

        if not result:
            return 1

    try:
        # Launch GUI
        print("✅ All checks passed, launching GUI...")
        print("✅ 所有检查通过，启动GUI...")

        # Try advanced GUI first, then enhanced, then basic
        try:
            from sam_gui_advanced import main as advanced_gui_main
            print("✅ Launching Advanced GUI with all features...")
            advanced_gui_main()
        except ImportError:
            try:
                from sam_gui_enhanced import main as enhanced_gui_main
                print("Advanced GUI not available, using enhanced GUI...")
                enhanced_gui_main()
            except ImportError:
                print("Enhanced GUI not available, using basic GUI...")
                from sam_gui import main as gui_main
                gui_main()

    except Exception as e:
        print(f"❌ Failed to launch GUI: {e}")
        print(f"❌ 启动GUI失败: {e}")

        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "Launch Failed / 启动失败",
            f"Failed to launch GUI / 启动GUI失败:\n{e}"
        )
        root.destroy()

        return 1

    return 0

if __name__ == "__main__":
    exit(main())
