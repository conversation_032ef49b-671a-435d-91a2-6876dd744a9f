#!/usr/bin/env python3
"""
SAM种子分割工具 - 增强版GUI界面
SAM Seed Segmentation Tool - Enhanced GUI Interface
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import os
import time
import sys
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageTk
import cv2
import numpy as np

# 导入基础GUI
from sam_gui import SAMGUIApp

class ToolTip:
    """工具提示类"""
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)
    
    def on_enter(self, event=None):
        x, y, _, _ = self.widget.bbox("insert") if hasattr(self.widget, 'bbox') else (0, 0, 0, 0)
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25
        
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry(f"+{x}+{y}")
        
        label = tk.Label(self.tooltip, text=self.text, background="lightyellow", 
                        relief="solid", borderwidth=1, font=("Arial", 9))
        label.pack()
    
    def on_leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

class EnhancedSAMGUIApp(SAMGUIApp):
    """增强版SAM GUI应用程序"""
    
    def __init__(self, root):
        # 先加载配置
        self.load_gui_config()
        
        # 调用父类初始化
        super().__init__(root)
        
        # 添加增强功能
        self.setup_enhanced_features()
    
    def load_gui_config(self):
        """加载GUI配置"""
        config_file = "sam_gui_config.json"
        self.gui_config = {}
        
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.gui_config = json.load(f)
            except Exception as e:
                print(f"加载GUI配置失败: {e}")
    
    def setup_enhanced_features(self):
        """设置增强功能"""
        # 添加预设配置菜单
        self.create_preset_menu()
        
        # 添加工具提示
        self.add_tooltips()
        
        # 设置窗口大小
        if "gui_settings" in self.gui_config:
            gui_settings = self.gui_config["gui_settings"]
            width = gui_settings.get("window_width", 1200)
            height = gui_settings.get("window_height", 800)
            self.root.geometry(f"{width}x{height}")
    
    def create_preset_menu(self):
        """创建预设配置菜单"""
        # 创建菜单栏
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件 / File", menu=file_menu)
        file_menu.add_command(label="新建配置 / New Config", command=self.new_config)
        file_menu.add_command(label="打开配置 / Open Config", command=self.load_config)
        file_menu.add_command(label="保存配置 / Save Config", command=self.save_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出 / Exit", command=self.root.quit)
        
        # 预设菜单
        preset_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="预设 / Presets", menu=preset_menu)
        
        if "presets" in self.gui_config:
            for preset_key, preset_data in self.gui_config["presets"].items():
                preset_menu.add_command(
                    label=preset_data["name"],
                    command=lambda key=preset_key: self.apply_preset(key)
                )
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具 / Tools", menu=tools_menu)
        tools_menu.add_command(label="检查环境 / Check Environment", command=self.check_environment)
        tools_menu.add_command(label="下载模型 / Download Model", command=self.download_model_help)
        tools_menu.add_command(label="批量重命名 / Batch Rename", command=self.batch_rename_help)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助 / Help", menu=help_menu)
        help_menu.add_command(label="使用说明 / User Guide", command=self.show_user_guide)
        help_menu.add_command(label="参数说明 / Parameter Help", command=self.show_parameter_help)
        help_menu.add_command(label="关于 / About", command=self.show_about)
    
    def add_tooltips(self):
        """添加工具提示"""
        if "tooltips" in self.gui_config:
            lang = self.gui_config.get("gui_settings", {}).get("language", "zh")
            tooltips = self.gui_config["tooltips"].get(lang, {})
            
            # 这里可以为各个控件添加工具提示
            # 由于控件是在父类中创建的，这里只是示例
            pass
    
    def apply_preset(self, preset_key):
        """应用预设配置"""
        if "presets" not in self.gui_config or preset_key not in self.gui_config["presets"]:
            return
        
        preset = self.gui_config["presets"][preset_key]
        
        # 应用SAM参数
        if "sam_parameters" in preset:
            sam_params = preset["sam_parameters"]
            self.points_per_side.set(sam_params.get("points_per_side", self.points_per_side.get()))
            self.pred_iou_thresh.set(sam_params.get("pred_iou_thresh", self.pred_iou_thresh.get()))
            self.stability_score_thresh.set(sam_params.get("stability_score_thresh", self.stability_score_thresh.get()))
            self.min_mask_region_area.set(sam_params.get("min_mask_region_area", self.min_mask_region_area.get()))
        
        # 应用种子过滤参数
        if "seed_filtering" in preset:
            seed_params = preset["seed_filtering"]
            self.min_seed_area.set(seed_params.get("min_seed_area", self.min_seed_area.get()))
            self.max_seed_area.set(seed_params.get("max_seed_area", self.max_seed_area.get()))
            self.min_aspect_ratio.set(seed_params.get("min_aspect_ratio", self.min_aspect_ratio.get()))
            self.max_aspect_ratio.set(seed_params.get("max_aspect_ratio", self.max_aspect_ratio.get()))
            self.min_solidity.set(seed_params.get("min_solidity", self.min_solidity.get()))
        
        self.log_message(f"已应用预设: {preset['name']} / Applied preset: {preset['name']}")
    
    def new_config(self):
        """新建配置"""
        if messagebox.askyesno("确认 / Confirm", "确定要重置所有配置吗？ / Reset all configurations?"):
            self.load_default_config()
            self.log_message("已创建新配置 / Created new configuration")
    
    def check_environment(self):
        """检查环境"""
        try:
            from test_sam_setup import test_sam_installation, test_sam_model_loading
            
            # 创建新窗口显示检查结果
            check_window = tk.Toplevel(self.root)
            check_window.title("环境检查 / Environment Check")
            check_window.geometry("600x400")
            
            text_widget = scrolledtext.ScrolledText(check_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 重定向输出到文本控件
            import io
            import contextlib
            
            output = io.StringIO()
            with contextlib.redirect_stdout(output):
                install_ok = test_sam_installation()
                if install_ok:
                    model_ok = test_sam_model_loading()
            
            text_widget.insert(tk.END, output.getvalue())
            
        except Exception as e:
            messagebox.showerror("错误 / Error", f"环境检查失败: {e}")
    
    def download_model_help(self):
        """下载模型帮助"""
        help_text = """
SAM模型下载指南 / SAM Model Download Guide
==========================================

1. 官方下载链接 / Official Download:
   https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

2. GitHub链接 / GitHub Link:
   https://github.com/facebookresearch/segment-anything#model-checkpoints

3. 文件信息 / File Info:
   - 文件名: sam_vit_h_4b8939.pth
   - 大小: 约2.4GB
   - 模型: ViT-H (最高精度)

4. 安装位置 / Installation Location:
   将下载的文件放在工具目录中，与GUI程序同一文件夹

5. 其他模型选项 / Other Model Options:
   - sam_vit_l_0b3195.pth (1.2GB, 中等精度)
   - sam_vit_b_01ec64.pth (375MB, 较低精度但速度快)

注意：首次下载可能需要较长时间，请耐心等待。
Note: First download may take a long time, please be patient.
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("模型下载帮助 / Model Download Help")
        help_window.geometry("700x500")
        
        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
    
    def batch_rename_help(self):
        """批量重命名帮助"""
        help_text = """
批量重命名工具 / Batch Rename Tool
================================

如果您的图像文件名不符合标准格式，可以使用以下方法：

1. 标准格式 / Standard Format:
   S0000012-1.jpg (S + 物种编号 + - + 序号 + 扩展名)

2. 批量重命名脚本 / Batch Rename Script:
   可以创建Python脚本来批量重命名文件

3. 手动重命名 / Manual Rename:
   确保文件名包含物种标识符，便于分类

4. 支持的格式 / Supported Formats:
   - .jpg, .jpeg
   - .png
   - .bmp
   - .tiff

注意：重命名前请备份原始文件
Note: Please backup original files before renaming
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("批量重命名帮助 / Batch Rename Help")
        help_window.geometry("600x400")
        
        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
    
    def show_user_guide(self):
        """显示使用说明"""
        guide_text = """
SAM种子分割工具使用指南 / SAM Seed Segmentation User Guide
========================================================

1. 文件设置 / File Settings:
   - 选择包含种子图像的输入目录
   - 设置输出目录（结果保存位置）
   - 指定SAM模型文件路径

2. 参数设置 / Parameter Settings:
   - 使用预设配置快速开始
   - 根据种子特点调整过滤参数
   - 平衡精度和处理速度

3. 处理控制 / Processing Control:
   - 预览模式：先处理少量图像测试效果
   - 实时监控处理进度和统计信息
   - 可随时暂停、恢复或停止处理

4. 结果查看 / Result Viewing:
   - 查看调试图像确认检测效果
   - 检查裁剪的种子图像质量
   - 使用YOLO注释进行后续训练

5. 常见问题 / Common Issues:
   - 内存不足：使用CPU模式或减少批处理大小
   - 检测效果差：调整SAM参数或过滤条件
   - 处理速度慢：使用GPU或降低精度设置

更多详细信息请参考SAM_使用说明.md文件
For more details, please refer to SAM_使用说明.md file
        """
        
        guide_window = tk.Toplevel(self.root)
        guide_window.title("使用指南 / User Guide")
        guide_window.geometry("800x600")
        
        text_widget = scrolledtext.ScrolledText(guide_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, guide_text)
        text_widget.config(state=tk.DISABLED)
    
    def show_parameter_help(self):
        """显示参数说明"""
        param_text = """
参数详细说明 / Detailed Parameter Descriptions
============================================

SAM模型参数 / SAM Model Parameters:
---------------------------------
• Points per Side (采样点密度): 8-64
  控制分割精度，值越大越精细但速度越慢
  推荐：快速=16, 平衡=32, 高精度=64

• IoU Threshold (IoU阈值): 0.5-1.0
  控制掩码质量，值越高质量越好但可能遗漏对象
  推荐：0.85-0.92

• Stability Threshold (稳定性阈值): 0.5-1.0
  过滤不稳定的分割结果
  推荐：0.90-0.97

种子过滤参数 / Seed Filtering Parameters:
--------------------------------------
• Min/Max Seed Area (种子面积范围):
  根据实际种子大小设置，单位为像素
  小种子：200-50000, 大种子：2000-1000000

• Aspect Ratio (纵横比): 0.1-10.0
  长宽比范围，适应不同种子形状
  圆形种子：0.5-2.0, 椭圆种子：0.2-5.0

• Solidity (凸度): 0.1-1.0
  面积与凸包面积的比值，确保检测实心对象
  推荐：0.3-0.8

预设配置建议 / Preset Recommendations:
----------------------------------
• 高精度模式：适用于科研级别的精确分割
• 平衡模式：日常使用的最佳选择
• 快速模式：大批量处理时使用
• 大种子模式：针对大型种子优化
• 小种子模式：针对小型种子优化
        """
        
        param_window = tk.Toplevel(self.root)
        param_window.title("参数说明 / Parameter Help")
        param_window.geometry("800x600")
        
        text_widget = scrolledtext.ScrolledText(param_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, param_text)
        text_widget.config(state=tk.DISABLED)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
SAM种子分割工具 / SAM Seed Segmentation Tool
==========================================

版本 / Version: 1.0.0
开发者 / Developer: AI Assistant
基于 / Based on: Meta's Segment Anything Model (SAM)

功能特点 / Features:
• 高精度种子分割和裁剪
• 用户友好的图形界面
• 实时处理进度监控
• 多种预设配置
• YOLO格式注释输出
• 批量处理支持

技术栈 / Tech Stack:
• Python 3.8+
• Segment Anything Model
• OpenCV
• Tkinter GUI
• PyTorch

许可证 / License:
本工具基于开源许可证，仅供学习和研究使用
This tool is based on open source license, for learning and research only

联系方式 / Contact:
如有问题或建议，请通过GitHub Issues反馈
For questions or suggestions, please feedback via GitHub Issues

感谢使用！/ Thank you for using!
        """
        
        messagebox.showinfo("关于 / About", about_text)

def main():
    """主函数"""
    root = tk.Tk()
    app = EnhancedSAMGUIApp(root)
    
    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass
    
    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()
