#!/usr/bin/env python3
"""
测试SAM环境设置
Test SAM environment setup
"""

import sys
import torch
from pathlib import Path

def test_sam_installation():
    """测试SAM安装"""
    print("🔍 测试SAM环境设置...")
    print("🔍 Testing SAM environment setup...")
    print("="*50)
    
    # 1. 检查Python版本
    print(f"Python版本: {sys.version}")
    print(f"Python version: {sys.version}")
    
    # 2. 检查PyTorch
    try:
        print(f"PyTorch版本: {torch.__version__}")
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"CUDA version: {torch.version.cuda}")
    except ImportError:
        print("❌ PyTorch未安装")
        print("❌ PyTorch not installed")
        return False
    
    # 3. 检查segment-anything
    try:
        import segment_anything
        print(f"✅ segment-anything已安装")
        print(f"✅ segment-anything installed")
        
        from segment_anything import sam_model_registry
        print(f"✅ 可以导入sam_model_registry")
        print(f"✅ Can import sam_model_registry")
        
    except ImportError as e:
        print(f"❌ segment-anything未安装: {e}")
        print(f"❌ segment-anything not installed: {e}")
        print("请运行: pip install segment-anything")
        print("Please run: pip install segment-anything")
        return False
    
    # 4. 检查模型文件
    model_path = "sam_vit_h_4b8939.pth"
    if Path(model_path).exists():
        model_size = Path(model_path).stat().st_size / (1024**3)  # GB
        print(f"✅ SAM模型文件存在: {model_path} ({model_size:.1f} GB)")
        print(f"✅ SAM model file exists: {model_path} ({model_size:.1f} GB)")
    else:
        print(f"❌ SAM模型文件不存在: {model_path}")
        print(f"❌ SAM model file not found: {model_path}")
        print("请下载模型文件到当前目录")
        print("Please download the model file to current directory")
        return False
    
    # 5. 检查其他依赖
    required_packages = ['cv2', 'numpy', 'matplotlib', 'PIL']
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 可用")
            print(f"✅ {package} available")
        except ImportError:
            print(f"❌ {package} 未安装")
            print(f"❌ {package} not installed")
            return False
    
    print("\n🎉 所有依赖检查通过!")
    print("🎉 All dependencies check passed!")
    return True

def test_sam_model_loading():
    """测试SAM模型加载"""
    print("\n🔧 测试SAM模型加载...")
    print("🔧 Testing SAM model loading...")
    print("-"*30)
    
    try:
        from segment_anything import sam_model_registry
        
        # 设置设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {device}")
        print(f"Using device: {device}")
        
        # 加载模型
        model_path = "sam_vit_h_4b8939.pth"
        print(f"加载模型: {model_path}")
        print(f"Loading model: {model_path}")
        
        sam = sam_model_registry["vit_h"](checkpoint=model_path)
        sam.to(device=device)
        
        print("✅ SAM模型加载成功!")
        print("✅ SAM model loaded successfully!")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in sam.parameters())
        print(f"模型参数数量: {total_params:,}")
        print(f"Model parameters: {total_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ SAM模型加载失败: {e}")
        print(f"❌ SAM model loading failed: {e}")
        return False

def main():
    """主函数"""
    print("🌱 SAM种子分割工具 - 环境测试")
    print("🌱 SAM Seed Segmentation Tool - Environment Test")
    print("="*60)
    
    # 测试安装
    if not test_sam_installation():
        print("\n❌ 环境设置不完整，请安装缺失的依赖")
        print("❌ Environment setup incomplete, please install missing dependencies")
        return 1
    
    # 测试模型加载
    if not test_sam_model_loading():
        print("\n❌ 模型加载失败")
        print("❌ Model loading failed")
        return 1
    
    print("\n🎉 环境测试完成! SAM已准备就绪!")
    print("🎉 Environment test complete! SAM is ready!")
    print("\n📝 下一步:")
    print("📝 Next steps:")
    print("1. 运行: python sam_seed_segmenter.py \"CVH-seed-pic\" \"./sam_output\" --preview")
    print("2. 检查输出目录中的结果")
    print("3. 如果满意，移除 --preview 标志处理完整数据集")
    
    return 0

if __name__ == "__main__":
    exit(main())
