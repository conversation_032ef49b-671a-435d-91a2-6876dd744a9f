#!/usr/bin/env python3
"""
测试改进的种子检测配置
Test improved seed detection configuration
"""

import cv2
import numpy as np
import json
from pathlib import Path
from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig

def test_single_image(image_path: str, config_file: str = "config_large_seeds.json"):
    """测试单个图像的种子检测效果"""
    print(f"🔍 测试图像: {image_path}")
    print(f"🔍 Testing image: {image_path}")
    
    # 加载配置
    if Path(config_file).exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        seg_config = SegmentationConfig(**config_data['segmentation'])
        print("✓ 使用优化配置")
        print("✓ Using optimized configuration")
    else:
        seg_config = SegmentationConfig()
        print("⚠️ 使用默认配置")
        print("⚠️ Using default configuration")
    
    # 处理配置
    proc_config = ProcessingConfig(
        input_directory=".",
        output_directory="./test_output",
        preview_mode=True,
        save_debug_images=True
    )
    
    # 创建处理器
    processor = SeedProcessor(seg_config, proc_config)
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法加载图像: {image_path}")
        print(f"❌ Cannot load image: {image_path}")
        return False
    
    print(f"✓ 图像尺寸: {image.shape[1]}x{image.shape[0]}")
    print(f"✓ Image size: {image.shape[1]}x{image.shape[0]}")
    
    # 执行分割
    contours, debug_image = processor.segment_seeds(image)
    
    print(f"🎯 检测到 {len(contours)} 个种子")
    print(f"🎯 Detected {len(contours)} seeds")
    
    if len(contours) > 0:
        print("✅ 种子检测成功!")
        print("✅ Seed detection successful!")
        
        # 显示每个检测到的种子的信息
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = max(w, h) / min(w, h)
            extent = area / (w * h)
            
            print(f"  种子 {i+1}: 面积={area:.0f}, 尺寸={w}x{h}, 纵横比={aspect_ratio:.2f}, 填充度={extent:.2f}")
            print(f"  Seed {i+1}: area={area:.0f}, size={w}x{h}, aspect_ratio={aspect_ratio:.2f}, extent={extent:.2f}")
    else:
        print("❌ 未检测到种子")
        print("❌ No seeds detected")
        
        # 显示调试信息
        print("\n🔧 调试信息:")
        print("🔧 Debug info:")
        
        # 显示预处理结果
        preprocessed = processor.preprocess_image(image)
        print(f"  预处理图像范围: {preprocessed.min()}-{preprocessed.max()}")
        print(f"  Preprocessed image range: {preprocessed.min()}-{preprocessed.max()}")
        
        # 显示阈值结果
        binary = processor.threshold_image(preprocessed)
        foreground_pixels = np.sum(binary == 255)
        total_pixels = binary.size
        foreground_ratio = foreground_pixels / total_pixels
        print(f"  前景像素比例: {foreground_ratio:.3f} ({foreground_pixels}/{total_pixels})")
        print(f"  Foreground pixel ratio: {foreground_ratio:.3f} ({foreground_pixels}/{total_pixels})")
        
        # 显示形态学操作结果
        cleaned = processor.apply_morphological_operations(binary)
        cleaned_foreground = np.sum(cleaned == 255)
        print(f"  清理后前景像素: {cleaned_foreground}")
        print(f"  Cleaned foreground pixels: {cleaned_foreground}")
        
        # 查找所有轮廓（包括被过滤的）
        all_contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"  找到 {len(all_contours)} 个轮廓（过滤前）")
        print(f"  Found {len(all_contours)} contours (before filtering)")
        
        # 分析被过滤的轮廓
        for i, contour in enumerate(all_contours[:5]):  # 只显示前5个
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else float('inf')
            extent = area / (w * h) if w * h > 0 else 0
            
            # 检查过滤条件
            area_ok = seg_config.min_contour_area <= area <= seg_config.max_contour_area
            size_ok = (seg_config.min_seed_width <= w <= seg_config.max_seed_width and
                      seg_config.min_seed_height <= h <= seg_config.max_seed_height)
            aspect_ok = seg_config.min_aspect_ratio <= aspect_ratio <= seg_config.max_aspect_ratio
            extent_ok = extent >= seg_config.min_extent
            
            status = "✓" if all([area_ok, size_ok, aspect_ok, extent_ok]) else "❌"
            print(f"    轮廓 {i+1} {status}: 面积={area:.0f} 尺寸={w}x{h} 纵横比={aspect_ratio:.2f} 填充度={extent:.2f}")
            print(f"    Contour {i+1} {status}: area={area:.0f} size={w}x{h} aspect={aspect_ratio:.2f} extent={extent:.2f}")
            
            if not area_ok:
                print(f"      面积不符合: {area:.0f} 不在 [{seg_config.min_contour_area}, {seg_config.max_contour_area}]")
                print(f"      Area failed: {area:.0f} not in [{seg_config.min_contour_area}, {seg_config.max_contour_area}]")
            if not size_ok:
                print(f"      尺寸不符合: {w}x{h} 不在 [{seg_config.min_seed_width}-{seg_config.max_seed_width}] x [{seg_config.min_seed_height}-{seg_config.max_seed_height}]")
                print(f"      Size failed: {w}x{h} not in [{seg_config.min_seed_width}-{seg_config.max_seed_width}] x [{seg_config.min_seed_height}-{seg_config.max_seed_height}]")
            if not aspect_ok:
                print(f"      纵横比不符合: {aspect_ratio:.2f} 不在 [{seg_config.min_aspect_ratio}, {seg_config.max_aspect_ratio}]")
                print(f"      Aspect ratio failed: {aspect_ratio:.2f} not in [{seg_config.min_aspect_ratio}, {seg_config.max_aspect_ratio}]")
            if not extent_ok:
                print(f"      填充度不符合: {extent:.2f} < {seg_config.min_extent}")
                print(f"      Extent failed: {extent:.2f} < {seg_config.min_extent}")
    
    # 保存调试图像
    if debug_image is not None:
        debug_path = f"debug_{Path(image_path).name}"
        cv2.imwrite(debug_path, debug_image)
        print(f"💾 调试图像保存到: {debug_path}")
        print(f"💾 Debug image saved to: {debug_path}")
    
    # 保存中间处理步骤的图像
    preprocessed = processor.preprocess_image(image)
    cv2.imwrite(f"step1_preprocessed_{Path(image_path).name}", preprocessed)
    
    binary = processor.threshold_image(preprocessed)
    cv2.imwrite(f"step2_binary_{Path(image_path).name}", binary)
    
    cleaned = processor.apply_morphological_operations(binary)
    cv2.imwrite(f"step3_cleaned_{Path(image_path).name}", cleaned)
    
    print("💾 处理步骤图像已保存")
    print("💾 Processing step images saved")
    
    return len(contours) > 0

def main():
    """主函数"""
    # 测试图像路径
    test_images = [
        "CVH-seed-pic/S0000012-1.jpg",  # 您提到的图像
        # 可以添加更多测试图像
    ]
    
    print("🌱 种子检测改进测试")
    print("🌱 Seed Detection Improvement Test")
    print("="*50)
    
    success_count = 0
    for image_path in test_images:
        if Path(image_path).exists():
            success = test_single_image(image_path)
            if success:
                success_count += 1
            print("-" * 50)
        else:
            print(f"⚠️ 图像不存在: {image_path}")
            print(f"⚠️ Image not found: {image_path}")
    
    print(f"\n🎯 测试结果: {success_count}/{len(test_images)} 个图像成功检测到种子")
    print(f"🎯 Test results: {success_count}/{len(test_images)} images successfully detected seeds")

if __name__ == "__main__":
    main()
