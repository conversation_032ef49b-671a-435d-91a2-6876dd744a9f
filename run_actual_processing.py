#!/usr/bin/env python3
"""
Run actual seed processing and create real crop files.
This script will process real images and generate actual cropped seed images.
"""

import os
import sys
import traceback
from pathlib import Path

def run_processing():
    """Execute the actual seed processing."""
    try:
        print("🌱 STARTING ACTUAL SEED PROCESSING")
        print("="*60)
        
        # Import required modules
        import cv2
        import numpy as np
        import json
        import re
        from datetime import datetime
        
        print("✓ Imported required modules")
        
        # Create output structure
        output_dir = Path("output")
        if output_dir.exists():
            import shutil
            shutil.rmtree(output_dir)
            print("✓ Removed existing output directory")
        
        # Create directories
        dirs = [
            output_dir,
            output_dir / "crops",
            output_dir / "annotations",
            output_dir / "debug"
        ]
        
        for d in dirs:
            d.mkdir(parents=True, exist_ok=True)
        print("✓ Created output directory structure")
        
        # Find input images
        input_dir = Path("CVH-seed-pic")
        if not input_dir.exists():
            print("❌ Input directory not found")
            return False
        
        # Get first 3 JPG files
        jpg_files = [f for f in input_dir.glob("*.jpg") if f.is_file()][:3]
        if not jpg_files:
            print("❌ No JPG files found")
            return False
        
        print(f"✓ Found {len(jpg_files)} images to process:")
        for img in jpg_files:
            print(f"  • {img.name}")
        
        # Initialize processing variables
        species_class_map = {}
        results = []
        total_seeds = 0
        
        # Process each image
        for i, img_path in enumerate(jpg_files):
            print(f"\n{'='*60}")
            print(f"PROCESSING IMAGE {i+1}/3: {img_path.name}")
            print(f"{'='*60}")
            
            try:
                # Load image
                print("  📸 Loading image...")
                image = cv2.imread(str(img_path))
                if image is None:
                    print("  ❌ Failed to load image")
                    continue
                
                h, w = image.shape[:2]
                print(f"  ✓ Image loaded: {w}x{h} pixels")
                
                # Extract species ID from filename
                match = re.search(r'S(\d+)-', img_path.name)
                species_id = match.group(1) if match else f"unknown_{i}"
                
                if species_id not in species_class_map:
                    species_class_map[species_id] = len(species_class_map)
                class_id = species_class_map[species_id]
                
                print(f"  🏷️  Species: {species_id}, Class: {class_id}")
                
                # Image preprocessing
                print("  🔄 Preprocessing image...")
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                blurred = cv2.GaussianBlur(gray, (5, 5), 1.0)
                
                # Adaptive thresholding
                print("  🎯 Applying adaptive thresholding...")
                binary = cv2.adaptiveThreshold(
                    blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY, 11, 2
                )
                
                # Morphological operations
                print("  🔧 Applying morphological operations...")
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
                cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
                cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel, iterations=1)
                
                # Find contours
                print("  🔍 Finding contours...")
                contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                print(f"    Found {len(contours)} initial contours")
                
                # Filter contours
                print("  ✂️  Filtering contours...")
                good_contours = []
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 300 < area < 50000:  # Area filter
                        x, y, w_c, h_c = cv2.boundingRect(contour)
                        if (15 < w_c < 500 and 15 < h_c < 500 and  # Size filter
                            max(w_c, h_c) / min(w_c, h_c) < 5.0 and  # Aspect ratio filter
                            area / (w_c * h_c) > 0.3):  # Extent filter
                            good_contours.append(contour)
                
                print(f"    Filtered to {len(good_contours)} valid seed contours")
                
                if len(good_contours) == 0:
                    print("  ⚠️  No seeds detected in this image")
                    continue
                
                # Create species directory
                species_dir = output_dir / "crops" / f"species_{species_id}"
                species_dir.mkdir(parents=True, exist_ok=True)
                print(f"  📁 Created species directory: {species_dir.name}")
                
                # Extract and save crops
                print("  ✂️  Extracting seed crops...")
                base_name = img_path.stem
                bboxes = []
                saved_crops = []
                
                for j, contour in enumerate(good_contours):
                    x, y, w_c, h_c = cv2.boundingRect(contour)
                    bboxes.append((x, y, w_c, h_c))
                    
                    # Extract crop with padding
                    pad = 10
                    x1 = max(0, x - pad)
                    y1 = max(0, y - pad)
                    x2 = min(image.shape[1], x + w_c + pad)
                    y2 = min(image.shape[0], y + h_c + pad)
                    
                    crop = image[y1:y2, x1:x2]
                    
                    # Save crop
                    crop_name = f"{base_name}_seed_{j:03d}.jpg"
                    crop_path = species_dir / crop_name
                    
                    success = cv2.imwrite(str(crop_path), crop)
                    if success:
                        saved_crops.append(str(crop_path))
                        crop_h, crop_w = crop.shape[:2]
                        print(f"    ✓ Saved: {crop_name} ({crop_w}x{crop_h} pixels)")
                    else:
                        print(f"    ❌ Failed to save: {crop_name}")
                
                # Create YOLO annotation
                print("  📝 Creating YOLO annotation...")
                yolo_lines = []
                for x, y, w_c, h_c in bboxes:
                    center_x = (x + w_c/2) / w
                    center_y = (y + h_c/2) / h
                    norm_w = w_c / w
                    norm_h = h_c / h
                    yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_w:.6f} {norm_h:.6f}")
                
                # Save annotation file
                ann_file = output_dir / "annotations" / f"{base_name}.txt"
                with open(ann_file, 'w') as f:
                    f.write('\n'.join(yolo_lines))
                print(f"  ✓ Saved annotation: {ann_file.name} ({len(yolo_lines)} detections)")
                
                # Create debug image
                print("  🎨 Creating debug visualization...")
                debug_img = image.copy()
                cv2.drawContours(debug_img, good_contours, -1, (0, 255, 0), 2)
                for k, (x, y, w_c, h_c) in enumerate(bboxes):
                    cv2.rectangle(debug_img, (x, y), (x+w_c, y+h_c), (255, 0, 0), 2)
                    cv2.putText(debug_img, str(k), (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
                
                debug_path = output_dir / "debug" / f"debug_{img_path.name}"
                cv2.imwrite(str(debug_path), debug_img)
                print(f"  ✓ Saved debug image: {debug_path.name}")
                
                # Store results
                result = {
                    'filename': img_path.name,
                    'species_id': species_id,
                    'class_id': class_id,
                    'seeds_found': len(good_contours),
                    'saved_crops': saved_crops,
                    'annotation_file': str(ann_file),
                    'debug_image': str(debug_path)
                }
                results.append(result)
                total_seeds += len(good_contours)
                
                print(f"  ✅ Processing completed: {len(good_contours)} seeds extracted")
                
            except Exception as e:
                print(f"  ❌ Error processing {img_path.name}: {e}")
                traceback.print_exc()
        
        # Save metadata files
        print(f"\n💾 Saving metadata files...")
        
        # Class mapping
        class_mapping = {
            'species_to_class': species_class_map,
            'class_to_species': {v: k for k, v in species_class_map.items()},
            'total_classes': len(species_class_map),
            'created_at': datetime.now().isoformat()
        }
        
        with open(output_dir / "class_mapping.json", 'w') as f:
            json.dump(class_mapping, f, indent=2)
        print("✓ Saved class_mapping.json")
        
        # Processing summary
        summary = {
            'processing_date': datetime.now().isoformat(),
            'total_images_processed': len(results),
            'total_seeds_extracted': total_seeds,
            'average_seeds_per_image': total_seeds / len(results) if results else 0,
            'species_found': list(species_class_map.keys()),
            'results': results
        }
        
        with open(output_dir / "processing_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        print("✓ Saved processing_summary.json")
        
        # Final verification
        print(f"\n🔍 Verifying output files...")
        crop_files = list((output_dir / "crops").rglob("*.jpg"))
        ann_files = list((output_dir / "annotations").glob("*.txt"))
        debug_files = list((output_dir / "debug").glob("*.jpg"))
        
        print(f"✓ Crop files created: {len(crop_files)}")
        print(f"✓ Annotation files created: {len(ann_files)}")
        print(f"✓ Debug files created: {len(debug_files)}")
        
        # Display results
        print(f"\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"📊 Results Summary:")
        print(f"  • Images processed: {len(results)}")
        print(f"  • Total seeds extracted: {total_seeds}")
        print(f"  • Average seeds per image: {total_seeds / len(results) if results else 0:.1f}")
        print(f"  • Species identified: {len(species_class_map)}")
        
        if species_class_map:
            print(f"\n🏷️  Species Summary:")
            for species_id, class_id in species_class_map.items():
                species_seeds = sum(r['seeds_found'] for r in results if r['species_id'] == species_id)
                species_crops = len([f for f in crop_files if f"species_{species_id}" in str(f)])
                print(f"  • Species {species_id} (Class {class_id}): {species_seeds} seeds → {species_crops} crop files")
        
        if crop_files:
            print(f"\n📸 Sample crop files created:")
            for crop in crop_files[:8]:  # Show first 8
                rel_path = crop.relative_to(output_dir)
                print(f"  • {rel_path}")
        
        print(f"\n📁 All files saved to: {output_dir.absolute()}")
        return True
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_processing()
    if success:
        print("\n🎉 Seed processing completed successfully!")
        print("✅ Real cropped seed images have been generated!")
    else:
        print("\n❌ Seed processing failed!")
    
    print("\nPress Enter to continue...")
    input()
