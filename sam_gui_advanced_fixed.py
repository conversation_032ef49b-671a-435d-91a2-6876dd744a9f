#!/usr/bin/env python3
"""
SAM Seed Segmentation Tool - Advanced GUI (Fixed Version)
Fixed version that handles initialization order properly
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import os
import time
import sys
from pathlib import Path
from datetime import datetime

# Try to import optional modules
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL/Pillow not available. Image preview may not work.")

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("Warning: OpenCV not available. Some features may not work.")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("Warning: NumPy not available. Some features may not work.")

# Import backend modules
try:
    from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig, SAM_AVAILABLE
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"Backend import error: {e}")
    BACKEND_AVAILABLE = False

class LanguageManager:
    """Simple language manager"""
    
    def __init__(self, config_file="sam_gui_config.json"):
        self.config_file = config_file
        self.current_language = "en"
        self.languages = {
            'en': {
                'app_title': 'SAM Seed Segmentation Tool - Advanced',
                'file_settings': 'File Settings',
                'parameters': 'Parameters',
                'processing_control': 'Processing Control',
                'preview': 'Results Preview',
                'yolo_training': 'YOLO Training',
                'yolo_detection': 'YOLO Detection',
                'language': 'Language',
                'session_id': 'Session ID:',
                'input_dir': 'Input Directory:',
                'output_dir': 'Output Directory:',
                'browse': 'Browse',
                'start_processing': 'Start Processing',
                'stop': 'Stop',
                'refresh_preview': 'Refresh Preview'
            },
            'zh': {
                'app_title': 'SAM种子分割工具 - 高级版',
                'file_settings': '文件设置',
                'parameters': '参数设置',
                'processing_control': '处理控制',
                'preview': '结果预览',
                'yolo_training': 'YOLO训练',
                'yolo_detection': 'YOLO检测',
                'language': '语言',
                'session_id': '会话ID:',
                'input_dir': '输入目录:',
                'output_dir': '输出目录:',
                'browse': '浏览',
                'start_processing': '开始处理',
                'stop': '停止',
                'refresh_preview': '刷新预览'
            }
        }
        self.load_config()
    
    def load_config(self):
        """Load configuration"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'languages' in config:
                self.languages.update(config['languages'])
            
            gui_settings = config.get('gui_settings', {})
            self.current_language = gui_settings.get('language', 'en')
        except Exception as e:
            print(f"Config load error: {e}")
    
    def get_text(self, key, default=None):
        """Get text in current language"""
        if default is None:
            default = key
        return self.languages.get(self.current_language, {}).get(key, default)
    
    def set_language(self, language):
        """Set current language"""
        if language in self.languages:
            self.current_language = language

class SessionManager:
    """Simple session manager"""
    
    def __init__(self, base_output_dir):
        self.base_output_dir = Path(base_output_dir)
        self.current_session_id = None
        self.current_session_dir = None
    
    def create_new_session(self):
        """Create a new session"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.current_session_id = f"session_{timestamp}"
        self.current_session_dir = self.base_output_dir / self.current_session_id
        
        # Create directories
        session_dirs = ['crops', 'annotations', 'debug', 'logs', 'reports']
        for dir_name in session_dirs:
            (self.current_session_dir / dir_name).mkdir(parents=True, exist_ok=True)
        
        return self.current_session_id, self.current_session_dir

class SAMGUIAdvancedFixed:
    """Fixed version of advanced SAM GUI"""
    
    def __init__(self, root):
        self.root = root
        self.config_file = "sam_gui_config.json"
        
        # Initialize managers
        self.lang_mgr = LanguageManager(self.config_file)
        self.session_mgr = SessionManager("output")
        
        # Initialize variables
        self.init_variables()
        
        # Setup GUI
        self.setup_gui()
        
        # Create initial session
        self.create_new_session()
        
        # Log initial message
        self.log_message("Advanced SAM GUI initialized successfully")
    
    def init_variables(self):
        """Initialize variables"""
        self.input_dir = tk.StringVar(value="")
        self.output_dir = tk.StringVar(value="output")
        self.current_session_id = tk.StringVar()
        self.is_processing = False
    
    def setup_gui(self):
        """Setup GUI"""
        self.root.title(self.lang_mgr.get_text('app_title'))
        self.root.geometry("1200x800")
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create main interface
        self.create_main_interface()
        
        # Create status bar
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Language menu
        language_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.lang_mgr.get_text('language'), menu=language_menu)
        language_menu.add_command(label="English", command=lambda: self.change_language('en'))
        language_menu.add_command(label="中文", command=lambda: self.change_language('zh'))
    
    def create_main_interface(self):
        """Create main interface"""
        # Create notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # File Settings tab
        self.create_file_settings_tab()
        
        # Processing Control tab
        self.create_processing_tab()
        
        # Preview tab
        self.create_preview_tab()
        
        # YOLO Training tab (placeholder)
        self.create_yolo_training_tab()
        
        # YOLO Detection tab (placeholder)
        self.create_yolo_detection_tab()
    
    def create_file_settings_tab(self):
        """Create file settings tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.lang_mgr.get_text('file_settings'))
        
        # Session info
        session_frame = ttk.LabelFrame(frame, text="Session Management")
        session_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(session_frame, text=self.lang_mgr.get_text('session_id')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(session_frame, textvariable=self.current_session_id).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(session_frame, text="New Session", command=self.create_new_session).grid(row=0, column=2, padx=5, pady=5)
        
        # File paths
        paths_frame = ttk.LabelFrame(frame, text="File Paths")
        paths_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(paths_frame, text=self.lang_mgr.get_text('input_dir')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.lang_mgr.get_text('browse'), command=self.browse_input_dir).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(paths_frame, text=self.lang_mgr.get_text('output_dir')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.lang_mgr.get_text('browse'), command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=5)
    
    def create_processing_tab(self):
        """Create processing control tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.lang_mgr.get_text('processing_control'))
        
        # Control buttons
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_button = ttk.Button(control_frame, text=self.lang_mgr.get_text('start_processing'), command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text=self.lang_mgr.get_text('stop'), command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Processing log
        log_frame = ttk.LabelFrame(frame, text="Processing Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_preview_tab(self):
        """Create preview tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.lang_mgr.get_text('preview'))
        
        # Control frame
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_frame, text=self.lang_mgr.get_text('refresh_preview'), command=self.refresh_preview).pack(side=tk.LEFT, padx=5)
        
        # Preview area
        preview_frame = ttk.LabelFrame(frame, text="Preview Area")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.preview_text = scrolledtext.ScrolledText(preview_frame, height=15, wrap=tk.WORD)
        self.preview_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.preview_text.insert(tk.END, "Preview functionality will be available after processing images.\n")
    
    def create_yolo_training_tab(self):
        """Create YOLO training tab (placeholder)"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.lang_mgr.get_text('yolo_training'))
        
        ttk.Label(frame, text="YOLO Training Module", font=("Arial", 14, "bold")).pack(pady=20)
        ttk.Label(frame, text="This module will allow training YOLO models on processed seed data.").pack(pady=10)
        ttk.Label(frame, text="Features: Session selection, data validation, model configuration, training progress.").pack(pady=5)
    
    def create_yolo_detection_tab(self):
        """Create YOLO detection tab (placeholder)"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.lang_mgr.get_text('yolo_detection'))
        
        ttk.Label(frame, text="YOLO Detection Module", font=("Arial", 14, "bold")).pack(pady=20)
        ttk.Label(frame, text="This module will allow using trained YOLO models for seed detection.").pack(pady=10)
        ttk.Label(frame, text="Features: Model loading, batch detection, results visualization, statistics.").pack(pady=5)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_text = tk.StringVar(value="Ready")
        ttk.Label(self.status_bar, textvariable=self.status_text).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(self.status_bar, text=f"Language: {self.lang_mgr.current_language.upper()}").pack(side=tk.RIGHT, padx=5)
    
    def change_language(self, language):
        """Change language"""
        self.lang_mgr.set_language(language)
        self.log_message(f"Language changed to: {language}")
        messagebox.showinfo("Language Changed", f"Language changed to {language}. Please restart the application for full effect.")
    
    def create_new_session(self):
        """Create new session"""
        session_id, session_dir = self.session_mgr.create_new_session()
        self.current_session_id.set(session_id)
        self.log_message(f"Created new session: {session_id}")
        return session_id, session_dir
    
    def browse_input_dir(self):
        """Browse for input directory"""
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)
            self.log_message(f"Input directory set to: {directory}")
    
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)
            self.session_mgr = SessionManager(directory)
            self.log_message(f"Output directory set to: {directory}")
    
    def start_processing(self):
        """Start processing"""
        if not self.input_dir.get():
            messagebox.showwarning("Warning", "Please select an input directory")
            return
        
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        self.log_message("Processing started (demo mode)")
        
        # Simulate processing
        threading.Thread(target=self._simulate_processing, daemon=True).start()
    
    def stop_processing(self):
        """Stop processing"""
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.log_message("Processing stopped")
    
    def _simulate_processing(self):
        """Simulate processing"""
        for i in range(5):
            if not self.is_processing:
                break
            self.log_message(f"Processing step {i+1}/5...")
            time.sleep(1)
        
        if self.is_processing:
            self.log_message("Processing completed successfully!")
        
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def refresh_preview(self):
        """Refresh preview"""
        self.log_message("Refreshing preview...")
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(tk.END, f"Preview refreshed at {datetime.now().strftime('%H:%M:%S')}\n")
        self.preview_text.insert(tk.END, f"Session: {self.current_session_id.get()}\n")
        self.preview_text.insert(tk.END, "Preview functionality will show processed images here.\n")
    
    def log_message(self, message):
        """Log message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        else:
            print(log_entry.strip())
        
        if hasattr(self, 'status_text') and self.status_text:
            self.status_text.set(message)

def main():
    """Main function"""
    root = tk.Tk()
    app = SAMGUIAdvancedFixed(root)
    
    def on_closing():
        if app.is_processing:
            if messagebox.askokcancel("Quit", "Processing is running. Do you want to quit?"):
                app.stop_processing()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")

if __name__ == "__main__":
    main()
