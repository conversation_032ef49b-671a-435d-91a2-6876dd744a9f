#!/usr/bin/env python3
"""
SAM Seed Segmentation Tool - Advanced GUI (Fixed Version)
Fixed version that handles initialization order properly
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import os
import time
import sys
from pathlib import Path
from datetime import datetime

# Try to import optional modules
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL/Pillow not available. Image preview may not work.")

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("Warning: OpenCV not available. Some features may not work.")

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("Warning: NumPy not available. Some features may not work.")

# Import backend modules
try:
    from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig, SAM_AVAILABLE
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"Backend import error: {e}")
    BACKEND_AVAILABLE = False

class ChineseTextManager:
    """Chinese text manager - simplified for Chinese-only interface"""

    def __init__(self):
        self.texts = {
            'app_title': 'SAM种子分割工具 - 高级版',
            'file_settings': '文件设置',
            'parameters': '参数设置',
            'processing_control': '处理控制',
            'preview': '结果预览',
            'yolo_training': 'YOLO训练',
            'yolo_detection': 'YOLO检测',
            'session_id': '会话ID:',
            'input_dir': '输入图像目录:',
            'output_dir': '输出目录:',
            'model_path': 'SAM模型文件:',
            'browse': '浏览',
            'start_processing': '开始处理',
            'stop': '停止',
            'refresh_preview': '刷新预览',
            'sam_parameters': 'SAM分割参数',
            'seed_filtering': '种子过滤参数',
            'points_per_side': '每边点数:',
            'pred_iou_thresh': '预测IoU阈值:',
            'stability_score_thresh': '稳定性分数阈值:',
            'min_seed_area': '最小种子面积:',
            'max_seed_area': '最大种子面积:',
            'min_aspect_ratio': '最小长宽比:',
            'max_aspect_ratio': '最大长宽比:',
            'min_solidity': '最小实心度:',
            'device': '计算设备:',
            'preview_mode': '预览模式',
            'max_preview': '最大预览图像数:',
            'save_debug': '保存调试图像',
            'create_yolo': '创建YOLO注释',
            'high_precision': '高精度',
            'balanced': '平衡',
            'fast': '快速',
            'large_seeds': '大种子',
            'small_seeds': '小种子',
            'load_model': '加载模型',
            'load_dataset': '加载数据集',
            'import_external': '导入外部数据',
            'train_model': '训练模型',
            'detect_objects': '目标检测',
            'model_file': '模型文件:',
            'dataset_path': '数据集路径:',
            'confidence_thresh': '置信度阈值:',
            'nms_thresh': 'NMS阈值:',
            'epochs': '训练轮数:',
            'batch_size': '批次大小:',
            'learning_rate': '学习率:',
            'validation_split': '验证集比例:'
        }

    def get_text(self, key, default=None):
        """Get Chinese text"""
        if default is None:
            default = key
        return self.texts.get(key, default)

class SessionManager:
    """Simple session manager"""

    def __init__(self, base_output_dir):
        self.base_output_dir = Path(base_output_dir)
        self.current_session_id = None
        self.current_session_dir = None

    def create_new_session(self):
        """Create a new session"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.current_session_id = f"session_{timestamp}"
        self.current_session_dir = self.base_output_dir / self.current_session_id

        # Create directories
        session_dirs = ['crops', 'annotations', 'debug', 'logs', 'reports']
        for dir_name in session_dirs:
            (self.current_session_dir / dir_name).mkdir(parents=True, exist_ok=True)

        return self.current_session_id, self.current_session_dir

    def get_session_list(self):
        """Get list of existing sessions"""
        if not self.base_output_dir.exists():
            return []

        sessions = []
        for item in self.base_output_dir.iterdir():
            if item.is_dir() and item.name.startswith('session_'):
                sessions.append(item.name)

        return sorted(sessions, reverse=True)  # Most recent first

class SAMGUIAdvancedFixed:
    """Fixed version of advanced SAM GUI - Chinese interface with manual parameters"""

    def __init__(self, root):
        self.root = root
        self.config_file = "sam_gui_config.json"

        # Initialize managers
        self.text_mgr = ChineseTextManager()
        self.session_mgr = SessionManager("output")

        # Load configuration
        self.load_config()

        # Initialize variables
        self.init_variables()

        # Setup GUI
        self.setup_gui()

        # Create initial session
        self.create_new_session()

        # Log initial message
        self.log_message("SAM高级GUI初始化成功")
    
    def load_config(self):
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            print(f"配置加载错误: {e}")
            self.config = {}

    def init_variables(self):
        """Initialize variables"""
        # File paths
        self.input_dir = tk.StringVar(value=self.config.get('input_dir', ''))
        self.output_dir = tk.StringVar(value=self.config.get('output_dir', 'output'))
        self.model_path = tk.StringVar(value=self.config.get('model_path', 'sam_vit_h_4b8939.pth'))
        self.current_session_id = tk.StringVar()

        # Processing settings
        self.device = tk.StringVar(value=self.config.get('device', 'cpu'))
        self.preview_mode = tk.BooleanVar(value=self.config.get('preview_mode', True))
        self.max_preview_images = tk.IntVar(value=self.config.get('max_preview_images', 10))
        self.save_debug_images = tk.BooleanVar(value=self.config.get('save_debug_images', True))
        self.create_yolo_annotations = tk.BooleanVar(value=self.config.get('create_yolo_annotations', True))

        # SAM parameters
        self.points_per_side = tk.IntVar(value=self.config.get('points_per_side', 32))
        self.pred_iou_thresh = tk.DoubleVar(value=self.config.get('pred_iou_thresh', 0.88))
        self.stability_score_thresh = tk.DoubleVar(value=self.config.get('stability_score_thresh', 0.95))

        # Seed filtering parameters
        self.min_seed_area = tk.IntVar(value=self.config.get('min_seed_area', 100))
        self.max_seed_area = tk.IntVar(value=self.config.get('max_seed_area', 50000))
        self.min_aspect_ratio = tk.DoubleVar(value=self.config.get('min_aspect_ratio', 0.3))
        self.max_aspect_ratio = tk.DoubleVar(value=self.config.get('max_aspect_ratio', 3.0))
        self.min_solidity = tk.DoubleVar(value=self.config.get('min_solidity', 0.8))

        # Processing state
        self.is_processing = False
    
    def setup_gui(self):
        """Setup GUI"""
        self.root.title(self.text_mgr.get_text('app_title'))
        self.root.geometry("1400x900")

        # Create menu bar
        self.create_menu_bar()

        # Create main interface
        self.create_main_interface()

        # Create status bar
        self.create_status_bar()

    def create_menu_bar(self):
        """Create menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="加载配置", command=self.load_config_file)
        file_menu.add_command(label="保存配置", command=self.save_config_file)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_main_interface(self):
        """Create main interface"""
        # Create notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # File Settings tab
        self.create_file_settings_tab()

        # Parameters tab
        self.create_parameters_tab()

        # Processing Control tab
        self.create_processing_tab()

        # Preview tab
        self.create_preview_tab()

        # YOLO Training tab
        self.create_yolo_training_tab()

        # YOLO Detection tab
        self.create_yolo_detection_tab()
    
    def create_file_settings_tab(self):
        """Create file settings tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('file_settings'))

        # Session info
        session_frame = ttk.LabelFrame(frame, text="会话管理")
        session_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(session_frame, text=self.text_mgr.get_text('session_id')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(session_frame, textvariable=self.current_session_id).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(session_frame, text="新建会话", command=self.create_new_session).grid(row=0, column=2, padx=5, pady=5)

        # File paths
        paths_frame = ttk.LabelFrame(frame, text="文件路径")
        paths_frame.pack(fill=tk.X, padx=5, pady=5)

        # Input directory
        ttk.Label(paths_frame, text=self.text_mgr.get_text('input_dir')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.text_mgr.get_text('browse'), command=self.browse_input_dir).grid(row=0, column=2, padx=5, pady=5)

        # Output directory
        ttk.Label(paths_frame, text=self.text_mgr.get_text('output_dir')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.text_mgr.get_text('browse'), command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=5)

        # Model path
        ttk.Label(paths_frame, text=self.text_mgr.get_text('model_path')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.model_path, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.text_mgr.get_text('browse'), command=self.browse_model_path).grid(row=2, column=2, padx=5, pady=5)

        # Processing options
        options_frame = ttk.LabelFrame(frame, text="处理选项")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(options_frame, text=self.text_mgr.get_text('device')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        device_combo = ttk.Combobox(options_frame, textvariable=self.device, values=['cpu', 'cuda'], state="readonly")
        device_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Checkbutton(options_frame, text=self.text_mgr.get_text('preview_mode'), variable=self.preview_mode).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        ttk.Label(options_frame, text=self.text_mgr.get_text('max_preview')).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(options_frame, from_=1, to=100, textvariable=self.max_preview_images, width=10).grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        ttk.Checkbutton(options_frame, text=self.text_mgr.get_text('save_debug'), variable=self.save_debug_images).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(options_frame, text=self.text_mgr.get_text('create_yolo'), variable=self.create_yolo_annotations).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

    def create_parameters_tab(self):
        """Create parameters tab with manual controls"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('parameters'))

        # SAM Parameters
        sam_frame = ttk.LabelFrame(frame, text=self.text_mgr.get_text('sam_parameters'))
        sam_frame.pack(fill=tk.X, padx=5, pady=5)

        # Points per side
        ttk.Label(sam_frame, text=self.text_mgr.get_text('points_per_side')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(sam_frame, from_=8, to=128, textvariable=self.points_per_side, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(sam_frame, text="(8-128, 值越大越精细但速度越慢)").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # Prediction IoU threshold
        ttk.Label(sam_frame, text=self.text_mgr.get_text('pred_iou_thresh')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(sam_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.pred_iou_thresh, length=200).grid(row=1, column=1, padx=5, pady=5)
        pred_iou_label = ttk.Label(sam_frame, text="0.88")
        pred_iou_label.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.pred_iou_thresh.trace('w', lambda *args: pred_iou_label.config(text=f"{self.pred_iou_thresh.get():.2f}"))

        # Stability score threshold
        ttk.Label(sam_frame, text=self.text_mgr.get_text('stability_score_thresh')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(sam_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.stability_score_thresh, length=200).grid(row=2, column=1, padx=5, pady=5)
        stability_label = ttk.Label(sam_frame, text="0.95")
        stability_label.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.stability_score_thresh.trace('w', lambda *args: stability_label.config(text=f"{self.stability_score_thresh.get():.2f}"))

        # Seed filtering parameters
        filter_frame = ttk.LabelFrame(frame, text=self.text_mgr.get_text('seed_filtering'))
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # Area filters
        ttk.Label(filter_frame, text=self.text_mgr.get_text('min_seed_area')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(filter_frame, from_=10, to=10000, textvariable=self.min_seed_area, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(filter_frame, text="像素").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        ttk.Label(filter_frame, text=self.text_mgr.get_text('max_seed_area')).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(filter_frame, from_=100, to=100000, textvariable=self.max_seed_area, width=10).grid(row=0, column=4, sticky=tk.W, padx=5, pady=5)
        ttk.Label(filter_frame, text="像素").grid(row=0, column=5, sticky=tk.W, padx=5, pady=5)

        # Aspect ratio filters
        ttk.Label(filter_frame, text=self.text_mgr.get_text('min_aspect_ratio')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(filter_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=self.min_aspect_ratio, length=150).grid(row=1, column=1, padx=5, pady=5)
        min_aspect_label = ttk.Label(filter_frame, text="0.30")
        min_aspect_label.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.min_aspect_ratio.trace('w', lambda *args: min_aspect_label.config(text=f"{self.min_aspect_ratio.get():.2f}"))

        ttk.Label(filter_frame, text=self.text_mgr.get_text('max_aspect_ratio')).grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(filter_frame, from_=1.0, to=5.0, orient=tk.HORIZONTAL, variable=self.max_aspect_ratio, length=150).grid(row=1, column=4, padx=5, pady=5)
        max_aspect_label = ttk.Label(filter_frame, text="3.00")
        max_aspect_label.grid(row=1, column=5, sticky=tk.W, padx=5, pady=5)
        self.max_aspect_ratio.trace('w', lambda *args: max_aspect_label.config(text=f"{self.max_aspect_ratio.get():.2f}"))

        # Solidity filter
        ttk.Label(filter_frame, text=self.text_mgr.get_text('min_solidity')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(filter_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.min_solidity, length=200).grid(row=2, column=1, padx=5, pady=5)
        solidity_label = ttk.Label(filter_frame, text="0.80")
        solidity_label.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.min_solidity.trace('w', lambda *args: solidity_label.config(text=f"{self.min_solidity.get():.2f}"))

        # Preset configurations
        preset_frame = ttk.LabelFrame(frame, text="预设配置")
        preset_frame.pack(fill=tk.X, padx=5, pady=5)

        presets = [
            (self.text_mgr.get_text('high_precision'), 'high_precision'),
            (self.text_mgr.get_text('balanced'), 'balanced'),
            (self.text_mgr.get_text('fast'), 'fast'),
            (self.text_mgr.get_text('large_seeds'), 'large_seeds'),
            (self.text_mgr.get_text('small_seeds'), 'small_seeds')
        ]

        for i, (preset_name, preset_key) in enumerate(presets):
            ttk.Button(preset_frame, text=preset_name, command=lambda p=preset_key: self.apply_preset(p)).grid(row=0, column=i, padx=5, pady=5)
    
    def create_processing_tab(self):
        """Create processing control tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('processing_control'))

        # Control buttons
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_button = ttk.Button(control_frame, text=self.text_mgr.get_text('start_processing'), command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(control_frame, text=self.text_mgr.get_text('stop'), command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # Progress display
        progress_frame = ttk.LabelFrame(frame, text="处理进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=400)
        self.progress_bar.pack(padx=5, pady=5)

        # Status labels
        status_frame = ttk.Frame(progress_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.current_image_label = ttk.Label(status_frame, text="当前图像: --")
        self.current_image_label.pack(side=tk.LEFT, padx=5)

        self.progress_label = ttk.Label(status_frame, text="进度: 0/0")
        self.progress_label.pack(side=tk.LEFT, padx=20)

        # Processing log
        log_frame = ttk.LabelFrame(frame, text="处理日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Log controls
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
    
    def create_preview_tab(self):
        """Create preview tab"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('preview'))

        # Control frame
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text=self.text_mgr.get_text('refresh_preview'), command=self.refresh_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="打开输出目录", command=self.open_output_directory).pack(side=tk.LEFT, padx=5)

        # Preview area
        preview_frame = ttk.LabelFrame(frame, text="预览区域")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.preview_text = scrolledtext.ScrolledText(preview_frame, height=15, wrap=tk.WORD)
        self.preview_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.preview_text.insert(tk.END, "处理图像后将在此显示预览功能。\n使用A/D键或方向键可以快速浏览图像。\n")
    
    def create_yolo_training_tab(self):
        """Create YOLO training tab with independence from SAM"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('yolo_training'))

        # Data source selection
        source_frame = ttk.LabelFrame(frame, text="数据源选择")
        source_frame.pack(fill=tk.X, padx=5, pady=5)

        # Session-based data
        session_subframe = ttk.Frame(source_frame)
        session_subframe.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(session_subframe, text="使用SAM处理会话:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.training_session_combo = ttk.Combobox(session_subframe, state="readonly", width=30)
        self.training_session_combo.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(session_subframe, text="刷新会话", command=self.refresh_training_sessions).grid(row=0, column=2, padx=5, pady=5)

        # External dataset
        external_subframe = ttk.Frame(source_frame)
        external_subframe.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(external_subframe, text="或导入外部数据集:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.external_dataset_path = tk.StringVar()
        ttk.Entry(external_subframe, textvariable=self.external_dataset_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(external_subframe, text="浏览", command=self.browse_external_dataset).grid(row=0, column=2, padx=5, pady=5)

        # Model configuration
        model_frame = ttk.LabelFrame(frame, text="模型配置")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        # Base model selection
        ttk.Label(model_frame, text="基础模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.yolo_model_var = tk.StringVar(value="yolov8n")
        model_combo = ttk.Combobox(model_frame, textvariable=self.yolo_model_var,
                                  values=["yolov8n", "yolov8s", "yolov8m", "yolov8l", "yolov8x",
                                         "yolov9c", "yolov9e", "yolov10n", "yolov10s", "yolov10m",
                                         "yolov10l", "yolov10x", "yolov11n", "yolov11s", "yolov11m",
                                         "yolov11l", "yolov11x"], state="readonly")
        model_combo.grid(row=0, column=1, padx=5, pady=5)

        # Pre-trained model loading
        ttk.Label(model_frame, text="或加载预训练模型:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.pretrained_model_path = tk.StringVar()
        ttk.Entry(model_frame, textvariable=self.pretrained_model_path, width=40).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(model_frame, text="浏览", command=self.browse_pretrained_model).grid(row=1, column=2, padx=5, pady=5)

        # Training parameters
        params_frame = ttk.LabelFrame(frame, text="训练参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # Epochs
        ttk.Label(params_frame, text=self.text_mgr.get_text('epochs')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.epochs_var = tk.IntVar(value=100)
        ttk.Spinbox(params_frame, from_=10, to=1000, textvariable=self.epochs_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Batch size
        ttk.Label(params_frame, text=self.text_mgr.get_text('batch_size')).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.batch_size_var = tk.IntVar(value=16)
        ttk.Spinbox(params_frame, from_=1, to=64, textvariable=self.batch_size_var, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # Learning rate
        ttk.Label(params_frame, text=self.text_mgr.get_text('learning_rate')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.learning_rate_var = tk.DoubleVar(value=0.01)
        ttk.Entry(params_frame, textvariable=self.learning_rate_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Validation split
        ttk.Label(params_frame, text=self.text_mgr.get_text('validation_split')).grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.val_split_var = tk.DoubleVar(value=0.2)
        ttk.Scale(params_frame, from_=0.1, to=0.4, orient=tk.HORIZONTAL, variable=self.val_split_var, length=100).grid(row=1, column=3, padx=5, pady=5)

        # Training controls
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="验证数据", command=self.validate_training_data).pack(side=tk.LEFT, padx=5)
        self.start_training_button = ttk.Button(control_frame, text="开始训练", command=self.start_yolo_training)
        self.start_training_button.pack(side=tk.LEFT, padx=5)
        self.stop_training_button = ttk.Button(control_frame, text="停止训练", command=self.stop_yolo_training, state=tk.DISABLED)
        self.stop_training_button.pack(side=tk.LEFT, padx=5)

        # Training progress and log
        progress_frame = ttk.LabelFrame(frame, text="训练进度")
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.training_progress_var = tk.DoubleVar()
        self.training_progress_bar = ttk.Progressbar(progress_frame, variable=self.training_progress_var, maximum=100)
        self.training_progress_bar.pack(fill=tk.X, padx=5, pady=5)

        self.training_log = scrolledtext.ScrolledText(progress_frame, height=8, wrap=tk.WORD)
        self.training_log.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initialize
        self.refresh_training_sessions()
    
    def create_yolo_detection_tab(self):
        """Create YOLO detection tab with independence from SAM"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('yolo_detection'))

        # Model configuration
        model_frame = ttk.LabelFrame(frame, text="模型配置")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(model_frame, text="YOLO模型文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.detection_model_path = tk.StringVar()
        ttk.Entry(model_frame, textvariable=self.detection_model_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(model_frame, text="浏览", command=self.browse_detection_model).grid(row=0, column=2, padx=5, pady=5)

        # Model info display
        self.model_info_text = scrolledtext.ScrolledText(model_frame, height=3, wrap=tk.WORD)
        self.model_info_text.grid(row=1, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        self.model_info_text.insert(tk.END, "选择模型文件后将显示模型信息...")

        # Detection parameters
        params_frame = ttk.LabelFrame(frame, text="检测参数")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # Confidence threshold
        ttk.Label(params_frame, text=self.text_mgr.get_text('confidence_thresh')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.confidence_var = tk.DoubleVar(value=0.25)
        ttk.Scale(params_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=self.confidence_var, length=200).grid(row=0, column=1, padx=5, pady=5)
        conf_label = ttk.Label(params_frame, text="0.25")
        conf_label.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.confidence_var.trace('w', lambda *args: conf_label.config(text=f"{self.confidence_var.get():.2f}"))

        # NMS threshold
        ttk.Label(params_frame, text=self.text_mgr.get_text('nms_thresh')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.nms_var = tk.DoubleVar(value=0.45)
        ttk.Scale(params_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=self.nms_var, length=200).grid(row=1, column=1, padx=5, pady=5)
        nms_label = ttk.Label(params_frame, text="0.45")
        nms_label.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.nms_var.trace('w', lambda *args: nms_label.config(text=f"{self.nms_var.get():.2f}"))

        # Input/Output configuration
        io_frame = ttk.LabelFrame(frame, text="输入输出配置")
        io_frame.pack(fill=tk.X, padx=5, pady=5)

        # Input directory
        ttk.Label(io_frame, text="输入图像目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.detection_input_dir = tk.StringVar()
        ttk.Entry(io_frame, textvariable=self.detection_input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="浏览", command=self.browse_detection_input).grid(row=0, column=2, padx=5, pady=5)

        # Output directory
        ttk.Label(io_frame, text="输出结果目录:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.detection_output_dir = tk.StringVar()
        ttk.Entry(io_frame, textvariable=self.detection_output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="浏览", command=self.browse_detection_output).grid(row=1, column=2, padx=5, pady=5)

        # Detection controls
        control_frame = ttk.Frame(frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="验证模型", command=self.validate_detection_model).pack(side=tk.LEFT, padx=5)
        self.start_detection_button = ttk.Button(control_frame, text="开始检测", command=self.start_yolo_detection)
        self.start_detection_button.pack(side=tk.LEFT, padx=5)
        self.stop_detection_button = ttk.Button(control_frame, text="停止检测", command=self.stop_yolo_detection, state=tk.DISABLED)
        self.stop_detection_button.pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="查看结果", command=self.view_detection_results).pack(side=tk.LEFT, padx=5)

        # Results display
        results_frame = ttk.LabelFrame(frame, text="检测结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create notebook for results
        self.detection_results_notebook = ttk.Notebook(results_frame)
        self.detection_results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Statistics tab
        stats_frame = ttk.Frame(self.detection_results_notebook)
        self.detection_results_notebook.add(stats_frame, text="统计信息")

        self.detection_stats_text = scrolledtext.ScrolledText(stats_frame, wrap=tk.WORD)
        self.detection_stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Log tab
        log_frame = ttk.Frame(self.detection_results_notebook)
        self.detection_results_notebook.add(log_frame, text="检测日志")

        self.detection_log = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD)
        self.detection_log.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_text = tk.StringVar(value="就绪")
        ttk.Label(self.status_bar, textvariable=self.status_text).pack(side=tk.LEFT, padx=5)

        # Session info
        ttk.Label(self.status_bar, text="SAM种子分割工具 - 高级版").pack(side=tk.RIGHT, padx=5)
    
    # File browser methods
    def browse_model_path(self):
        """Browse for SAM model file"""
        filename = filedialog.askopenfilename(
            title="选择SAM模型文件",
            filetypes=[("PyTorch模型", "*.pth"), ("所有文件", "*.*")]
        )
        if filename:
            self.model_path.set(filename)
            self.log_message(f"SAM模型文件设置为: {filename}")

    # Preset configuration methods
    def apply_preset(self, preset_name):
        """Apply preset configuration"""
        presets = {
            'high_precision': {
                'points_per_side': 64,
                'pred_iou_thresh': 0.92,
                'stability_score_thresh': 0.97,
                'min_seed_area': 50,
                'max_seed_area': 100000,
                'min_aspect_ratio': 0.2,
                'max_aspect_ratio': 5.0,
                'min_solidity': 0.85
            },
            'balanced': {
                'points_per_side': 32,
                'pred_iou_thresh': 0.88,
                'stability_score_thresh': 0.95,
                'min_seed_area': 100,
                'max_seed_area': 50000,
                'min_aspect_ratio': 0.3,
                'max_aspect_ratio': 3.0,
                'min_solidity': 0.8
            },
            'fast': {
                'points_per_side': 16,
                'pred_iou_thresh': 0.85,
                'stability_score_thresh': 0.92,
                'min_seed_area': 200,
                'max_seed_area': 30000,
                'min_aspect_ratio': 0.4,
                'max_aspect_ratio': 2.5,
                'min_solidity': 0.75
            },
            'large_seeds': {
                'points_per_side': 24,
                'pred_iou_thresh': 0.90,
                'stability_score_thresh': 0.95,
                'min_seed_area': 1000,
                'max_seed_area': 200000,
                'min_aspect_ratio': 0.3,
                'max_aspect_ratio': 3.0,
                'min_solidity': 0.8
            },
            'small_seeds': {
                'points_per_side': 48,
                'pred_iou_thresh': 0.86,
                'stability_score_thresh': 0.93,
                'min_seed_area': 20,
                'max_seed_area': 5000,
                'min_aspect_ratio': 0.25,
                'max_aspect_ratio': 4.0,
                'min_solidity': 0.7
            }
        }

        if preset_name in presets:
            preset = presets[preset_name]

            # Apply SAM parameters
            self.points_per_side.set(preset['points_per_side'])
            self.pred_iou_thresh.set(preset['pred_iou_thresh'])
            self.stability_score_thresh.set(preset['stability_score_thresh'])

            # Apply filtering parameters
            self.min_seed_area.set(preset['min_seed_area'])
            self.max_seed_area.set(preset['max_seed_area'])
            self.min_aspect_ratio.set(preset['min_aspect_ratio'])
            self.max_aspect_ratio.set(preset['max_aspect_ratio'])
            self.min_solidity.set(preset['min_solidity'])

            self.log_message(f"已应用预设配置: {preset_name}")

    # YOLO Training methods
    def refresh_training_sessions(self):
        """Refresh available training sessions"""
        sessions = self.session_mgr.get_session_list() if hasattr(self.session_mgr, 'get_session_list') else []
        if hasattr(self, 'training_session_combo'):
            self.training_session_combo['values'] = sessions
            if sessions:
                self.training_session_combo.set(sessions[0])

    def browse_external_dataset(self):
        """Browse for external YOLO dataset"""
        directory = filedialog.askdirectory(title="选择外部YOLO数据集目录")
        if directory:
            self.external_dataset_path.set(directory)
            self.log_training(f"外部数据集路径设置为: {directory}")

    def browse_pretrained_model(self):
        """Browse for pre-trained YOLO model"""
        filename = filedialog.askopenfilename(
            title="选择预训练YOLO模型",
            filetypes=[("PyTorch模型", "*.pt"), ("所有文件", "*.*")]
        )
        if filename:
            self.pretrained_model_path.set(filename)
            self.log_training(f"预训练模型设置为: {filename}")

    def validate_training_data(self):
        """Validate training data"""
        self.log_training("开始验证训练数据...")

        # Check session data or external data
        if self.training_session_combo.get():
            self.log_training(f"验证会话数据: {self.training_session_combo.get()}")
            # Add session data validation logic here
        elif self.external_dataset_path.get():
            dataset_path = Path(self.external_dataset_path.get())
            if dataset_path.exists():
                # Check for YOLO format structure
                images_found = len(list(dataset_path.rglob("*.jpg"))) + len(list(dataset_path.rglob("*.png")))
                labels_found = len(list(dataset_path.rglob("*.txt")))
                self.log_training(f"找到 {images_found} 个图像文件")
                self.log_training(f"找到 {labels_found} 个标签文件")

                if images_found > 0 and labels_found > 0:
                    self.log_training("✅ 数据验证通过，可以开始训练")
                else:
                    self.log_training("❌ 数据验证失败，请检查数据集格式")
            else:
                self.log_training("❌ 数据集路径不存在")
        else:
            self.log_training("❌ 请选择训练数据源")

    def start_yolo_training(self):
        """Start YOLO training"""
        self.log_training("开始YOLO训练...")
        self.start_training_button.config(state=tk.DISABLED)
        self.stop_training_button.config(state=tk.NORMAL)

        # Simulate training process
        threading.Thread(target=self._simulate_yolo_training, daemon=True).start()

    def stop_yolo_training(self):
        """Stop YOLO training"""
        self.log_training("停止YOLO训练")
        self.start_training_button.config(state=tk.NORMAL)
        self.stop_training_button.config(state=tk.DISABLED)

    def _simulate_yolo_training(self):
        """Simulate YOLO training process"""
        epochs = self.epochs_var.get()
        for epoch in range(epochs):
            if hasattr(self, 'stop_training_button') and self.stop_training_button['state'] == tk.DISABLED:
                break

            progress = (epoch + 1) / epochs * 100
            self.training_progress_var.set(progress)

            if epoch % 10 == 0:
                self.log_training(f"训练进度: Epoch {epoch + 1}/{epochs}")

            time.sleep(0.1)  # Simulate training time

        self.log_training("YOLO训练完成！")
        self.start_training_button.config(state=tk.NORMAL)
        self.stop_training_button.config(state=tk.DISABLED)

    def log_training(self, message):
        """Log training message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        if hasattr(self, 'training_log'):
            self.training_log.insert(tk.END, log_entry)
            self.training_log.see(tk.END)

    # YOLO Detection methods
    def browse_detection_model(self):
        """Browse for YOLO detection model"""
        filename = filedialog.askopenfilename(
            title="选择YOLO检测模型",
            filetypes=[("PyTorch模型", "*.pt"), ("所有文件", "*.*")]
        )
        if filename:
            self.detection_model_path.set(filename)
            self.log_detection(f"检测模型设置为: {filename}")
            self.load_model_info(filename)

    def load_model_info(self, model_path):
        """Load and display model information"""
        try:
            model_file = Path(model_path)
            if model_file.exists():
                file_size = model_file.stat().st_size / (1024 * 1024)  # MB
                info_text = f"模型文件: {model_file.name}\n"
                info_text += f"文件大小: {file_size:.1f} MB\n"
                info_text += f"修改时间: {datetime.fromtimestamp(model_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')}\n"

                if hasattr(self, 'model_info_text'):
                    self.model_info_text.delete(1.0, tk.END)
                    self.model_info_text.insert(tk.END, info_text)
        except Exception as e:
            self.log_detection(f"加载模型信息失败: {e}")

    def browse_detection_input(self):
        """Browse for detection input directory"""
        directory = filedialog.askdirectory(title="选择检测输入图像目录")
        if directory:
            self.detection_input_dir.set(directory)
            self.log_detection(f"检测输入目录设置为: {directory}")

    def browse_detection_output(self):
        """Browse for detection output directory"""
        directory = filedialog.askdirectory(title="选择检测结果输出目录")
        if directory:
            self.detection_output_dir.set(directory)
            self.log_detection(f"检测输出目录设置为: {directory}")

    def validate_detection_model(self):
        """Validate detection model"""
        model_path = self.detection_model_path.get()
        if not model_path:
            self.log_detection("❌ 请先选择检测模型文件")
            return

        if not Path(model_path).exists():
            self.log_detection("❌ 模型文件不存在")
            return

        self.log_detection("✅ 模型文件验证通过")
        self.log_detection(f"置信度阈值: {self.confidence_var.get():.2f}")
        self.log_detection(f"NMS阈值: {self.nms_var.get():.2f}")

    def start_yolo_detection(self):
        """Start YOLO detection"""
        if not self.detection_model_path.get():
            messagebox.showwarning("警告", "请先选择检测模型文件")
            return

        if not self.detection_input_dir.get():
            messagebox.showwarning("警告", "请先选择输入图像目录")
            return

        if not self.detection_output_dir.get():
            messagebox.showwarning("警告", "请先选择输出结果目录")
            return

        self.log_detection("开始YOLO目标检测...")
        self.start_detection_button.config(state=tk.DISABLED)
        self.stop_detection_button.config(state=tk.NORMAL)

        # Simulate detection process
        threading.Thread(target=self._simulate_yolo_detection, daemon=True).start()

    def stop_yolo_detection(self):
        """Stop YOLO detection"""
        self.log_detection("停止YOLO检测")
        self.start_detection_button.config(state=tk.NORMAL)
        self.stop_detection_button.config(state=tk.DISABLED)

    def _simulate_yolo_detection(self):
        """Simulate YOLO detection process"""
        try:
            input_path = Path(self.detection_input_dir.get())
            image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))

            total_detections = 0
            for i, img_file in enumerate(image_files[:10]):  # Limit for demo
                if hasattr(self, 'stop_detection_button') and self.stop_detection_button['state'] == tk.DISABLED:
                    break

                # Simulate detection
                detections = np.random.randint(0, 5)  # Random number of detections
                total_detections += detections

                self.log_detection(f"处理 {img_file.name}: 检测到 {detections} 个目标")
                time.sleep(0.5)  # Simulate processing time

            # Update statistics
            stats_text = f"检测完成！\n\n"
            stats_text += f"处理图像数: {min(len(image_files), 10)}\n"
            stats_text += f"总检测数: {total_detections}\n"
            stats_text += f"平均每图检测数: {total_detections / min(len(image_files), 10) if image_files else 0:.1f}\n"
            stats_text += f"置信度阈值: {self.confidence_var.get():.2f}\n"
            stats_text += f"NMS阈值: {self.nms_var.get():.2f}\n"

            if hasattr(self, 'detection_stats_text'):
                self.detection_stats_text.delete(1.0, tk.END)
                self.detection_stats_text.insert(tk.END, stats_text)

            self.log_detection("YOLO检测完成！")

        except Exception as e:
            self.log_detection(f"检测过程出错: {e}")
        finally:
            self.start_detection_button.config(state=tk.NORMAL)
            self.stop_detection_button.config(state=tk.DISABLED)

    def view_detection_results(self):
        """View detection results"""
        output_dir = self.detection_output_dir.get()
        if output_dir and Path(output_dir).exists():
            self.open_directory(output_dir)
        else:
            messagebox.showwarning("警告", "输出目录不存在或未设置")

    def log_detection(self, message):
        """Log detection message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        if hasattr(self, 'detection_log'):
            self.detection_log.insert(tk.END, log_entry)
            self.detection_log.see(tk.END)
    
    def create_new_session(self):
        """Create new session"""
        session_id, session_dir = self.session_mgr.create_new_session()
        self.current_session_id.set(session_id)
        if hasattr(self, 'log_text'):
            self.log_message(f"创建新会话: {session_id}")
        else:
            print(f"创建新会话: {session_id}")
        return session_id, session_dir
    
    def browse_input_dir(self):
        """Browse for input directory"""
        directory = filedialog.askdirectory(title="选择输入图像目录")
        if directory:
            self.input_dir.set(directory)
            self.log_message(f"输入目录设置为: {directory}")

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)
            self.session_mgr = SessionManager(directory)
            self.log_message(f"输出目录设置为: {directory}")

    def open_directory(self, directory):
        """Open directory in file explorer"""
        try:
            if sys.platform == "win32":
                os.startfile(directory)
            elif sys.platform == "darwin":
                subprocess.run(["open", directory])
            else:
                subprocess.run(["xdg-open", directory])
        except Exception as e:
            self.log_message(f"打开目录失败: {e}")

    def open_output_directory(self):
        """Open output directory in file explorer"""
        if self.session_mgr.current_session_dir and self.session_mgr.current_session_dir.exists():
            self.open_directory(self.session_mgr.current_session_dir)
        else:
            messagebox.showwarning("警告", "输出目录不存在")

    # Configuration methods
    def load_config_file(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.log_message(f"配置已从文件加载: {filename}")
                self.update_gui_from_config()
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {e}")

    def save_config_file(self):
        """Save configuration to file"""
        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                self.update_config_from_gui()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                self.log_message(f"配置已保存到文件: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {e}")

    def update_gui_from_config(self):
        """Update GUI values from configuration"""
        # Update file paths
        self.input_dir.set(self.config.get('input_dir', ''))
        self.output_dir.set(self.config.get('output_dir', 'output'))
        self.model_path.set(self.config.get('model_path', 'sam_vit_h_4b8939.pth'))

        # Update processing settings
        self.device.set(self.config.get('device', 'cpu'))
        self.preview_mode.set(self.config.get('preview_mode', True))
        self.max_preview_images.set(self.config.get('max_preview_images', 10))
        self.save_debug_images.set(self.config.get('save_debug_images', True))
        self.create_yolo_annotations.set(self.config.get('create_yolo_annotations', True))

        # Update SAM parameters
        self.points_per_side.set(self.config.get('points_per_side', 32))
        self.pred_iou_thresh.set(self.config.get('pred_iou_thresh', 0.88))
        self.stability_score_thresh.set(self.config.get('stability_score_thresh', 0.95))

        # Update filtering parameters
        self.min_seed_area.set(self.config.get('min_seed_area', 100))
        self.max_seed_area.set(self.config.get('max_seed_area', 50000))
        self.min_aspect_ratio.set(self.config.get('min_aspect_ratio', 0.3))
        self.max_aspect_ratio.set(self.config.get('max_aspect_ratio', 3.0))
        self.min_solidity.set(self.config.get('min_solidity', 0.8))

    def update_config_from_gui(self):
        """Update configuration from GUI values"""
        # Update file paths
        self.config['input_dir'] = self.input_dir.get()
        self.config['output_dir'] = self.output_dir.get()
        self.config['model_path'] = self.model_path.get()

        # Update processing settings
        self.config['device'] = self.device.get()
        self.config['preview_mode'] = self.preview_mode.get()
        self.config['max_preview_images'] = self.max_preview_images.get()
        self.config['save_debug_images'] = self.save_debug_images.get()
        self.config['create_yolo_annotations'] = self.create_yolo_annotations.get()

        # Update SAM parameters
        self.config['points_per_side'] = self.points_per_side.get()
        self.config['pred_iou_thresh'] = self.pred_iou_thresh.get()
        self.config['stability_score_thresh'] = self.stability_score_thresh.get()

        # Update filtering parameters
        self.config['min_seed_area'] = self.min_seed_area.get()
        self.config['max_seed_area'] = self.max_seed_area.get()
        self.config['min_aspect_ratio'] = self.min_aspect_ratio.get()
        self.config['max_aspect_ratio'] = self.max_aspect_ratio.get()
        self.config['min_solidity'] = self.min_solidity.get()

    def show_about(self):
        """Show about dialog"""
        about_text = f"""SAM种子分割工具 - 高级版

版本: 2.0
基于Meta的Segment Anything Model (SAM)

功能特点:
• 高精度种子分割
• 会话管理和输出组织
• 手动参数调节
• 独立的YOLO训练模块
• 独立的YOLO检测模块
• 增强的结果预览系统

当前会话: {self.current_session_id.get()}

© 2024 SAM种子分割项目"""

        messagebox.showinfo("关于", about_text)
    
    def start_processing(self):
        """Start processing"""
        if not self.input_dir.get():
            messagebox.showwarning("警告", "请先选择输入图像目录")
            return

        if not Path(self.input_dir.get()).exists():
            messagebox.showerror("错误", "输入目录不存在")
            return

        if not Path(self.model_path.get()).exists():
            messagebox.showwarning("警告", "SAM模型文件不存在，请检查路径")
            return

        # Create new session if needed
        if not self.session_mgr.current_session_id:
            self.create_new_session()

        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        self.log_message("开始SAM种子分割处理...")

        # Start processing in separate thread
        threading.Thread(target=self._simulate_processing, daemon=True).start()

    def stop_processing(self):
        """Stop processing"""
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.log_message("处理已停止")

    def _simulate_processing(self):
        """Simulate processing"""
        try:
            input_path = Path(self.input_dir.get())
            image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))

            if not image_files:
                self.log_message("输入目录中未找到图像文件")
                return

            total_files = len(image_files)
            if self.preview_mode.get():
                image_files = image_files[:self.max_preview_images.get()]
                self.log_message(f"预览模式: 处理 {len(image_files)} 个图像")

            total_seeds = 0
            for i, img_file in enumerate(image_files):
                if not self.is_processing:
                    break

                # Update progress
                progress = (i + 1) / len(image_files) * 100
                self.progress_var.set(progress)
                self.current_image_label.config(text=f"当前图像: {img_file.name}")
                self.progress_label.config(text=f"进度: {i + 1}/{len(image_files)}")

                # Simulate processing
                detected_seeds = np.random.randint(1, 8)  # Random number of seeds
                total_seeds += detected_seeds

                self.log_message(f"处理 {img_file.name}: 检测到 {detected_seeds} 个种子")
                time.sleep(0.8)  # Simulate processing time

            if self.is_processing:
                self.log_message(f"SAM处理完成！总共检测到 {total_seeds} 个种子")
                self.refresh_preview()

        except Exception as e:
            self.log_message(f"处理过程出错: {e}")
        finally:
            self.is_processing = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.progress_var.set(0)
            self.current_image_label.config(text="当前图像: --")
            self.progress_label.config(text="进度: 0/0")

    def refresh_preview(self):
        """Refresh preview"""
        self.log_message("刷新预览...")
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(tk.END, f"预览刷新时间: {datetime.now().strftime('%H:%M:%S')}\n")
        self.preview_text.insert(tk.END, f"当前会话: {self.current_session_id.get()}\n\n")

        if self.session_mgr.current_session_dir:
            debug_dir = self.session_mgr.current_session_dir / "debug"
            if debug_dir.exists():
                debug_images = list(debug_dir.glob("*.jpg")) + list(debug_dir.glob("*.png"))
                if debug_images:
                    self.preview_text.insert(tk.END, f"找到 {len(debug_images)} 个调试图像\n")
                    self.preview_text.insert(tk.END, "使用A/D键或方向键可以快速浏览图像\n\n")

                    for img in debug_images[:5]:  # Show first 5
                        self.preview_text.insert(tk.END, f"• {img.name}\n")

                    if len(debug_images) > 5:
                        self.preview_text.insert(tk.END, f"... 还有 {len(debug_images) - 5} 个图像\n")
                else:
                    self.preview_text.insert(tk.END, "调试目录中未找到图像\n")
            else:
                self.preview_text.insert(tk.END, "调试目录不存在\n")
        else:
            self.preview_text.insert(tk.END, "请先运行SAM处理以生成预览图像\n")

    def clear_log(self):
        """Clear processing log"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def save_log(self):
        """Save processing log to file"""
        filename = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")
    
    def log_message(self, message):
        """Log message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        else:
            print(log_entry.strip())
        
        if hasattr(self, 'status_text') and self.status_text:
            self.status_text.set(message)

def main():
    """Main function"""
    root = tk.Tk()
    app = SAMGUIAdvancedFixed(root)

    def on_closing():
        if app.is_processing:
            if messagebox.askokcancel("退出", "正在处理中，确定要退出吗？"):
                app.stop_processing()
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("用户中断应用程序")
    except Exception as e:
        print(f"应用程序错误: {e}")

if __name__ == "__main__":
    main()
