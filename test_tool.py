#!/usr/bin/env python3
"""
Simple test script to verify the seed classification tool works.
"""

import os
import sys
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    try:
        import cv2
        import numpy as np
        import matplotlib.pyplot as plt
        from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_configuration():
    """Test configuration creation."""
    try:
        from seed_classifier import SegmentationConfig, ProcessingConfig
        
        seg_config = SegmentationConfig()
        proc_config = ProcessingConfig(
            input_directory="./test_input",
            output_directory="./test_output"
        )
        
        print("✓ Configuration creation successful")
        print(f"  - Threshold method: {seg_config.threshold_method}")
        print(f"  - Min contour area: {seg_config.min_contour_area}")
        print(f"  - Input directory: {proc_config.input_directory}")
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_processor_creation():
    """Test processor creation."""
    try:
        from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig
        
        seg_config = SegmentationConfig()
        proc_config = ProcessingConfig(
            input_directory="./test_input",
            output_directory="./test_output"
        )
        
        processor = SeedProcessor(seg_config, proc_config)
        print("✓ Processor creation successful")
        print(f"  - Species class map: {processor.species_class_map}")
        return True
    except Exception as e:
        print(f"✗ Processor creation error: {e}")
        return False

def test_file_discovery():
    """Test file discovery functionality."""
    try:
        from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig
        
        # Check if input directory exists
        input_dir = "CVH-seed-pic/CVH-seed-pic"
        if not os.path.exists(input_dir):
            print(f"⚠ Input directory not found: {input_dir}")
            return True  # Not a failure, just no test data
        
        seg_config = SegmentationConfig()
        proc_config = ProcessingConfig(
            input_directory=input_dir,
            output_directory="./test_output"
        )
        
        processor = SeedProcessor(seg_config, proc_config)
        image_files = processor.get_image_files()
        
        print(f"✓ File discovery successful")
        print(f"  - Found {len(image_files)} image files")
        if image_files:
            print(f"  - First file: {os.path.basename(image_files[0])}")
        
        return True
    except Exception as e:
        print(f"✗ File discovery error: {e}")
        return False

def test_species_extraction():
    """Test species ID extraction."""
    try:
        from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig
        
        seg_config = SegmentationConfig()
        proc_config = ProcessingConfig(
            input_directory="./test_input",
            output_directory="./test_output"
        )
        
        processor = SeedProcessor(seg_config, proc_config)
        
        # Test species extraction
        test_filenames = [
            "S0000002-1.jpg",
            "S0000123-1.jpg",
            "S0001234-1.jpg",
            "invalid_filename.jpg"
        ]
        
        print("✓ Species extraction test:")
        for filename in test_filenames:
            species_id = processor.extract_species_from_filename(filename)
            print(f"  - {filename} → {species_id}")
        
        return True
    except Exception as e:
        print(f"✗ Species extraction error: {e}")
        return False

def main():
    """Run all tests."""
    print("Seed Classification Tool - Test Suite")
    print("=" * 45)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Processor Creation Test", test_processor_creation),
        ("File Discovery Test", test_file_discovery),
        ("Species Extraction Test", test_species_extraction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        if test_func():
            passed += 1
    
    print(f"\n{'='*45}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The tool is ready to use.")
        print("\nNext steps:")
        print("1. Run: python preview_tool.py CVH-seed-pic/CVH-seed-pic --save-preview")
        print("2. Run: python seed_classifier.py CVH-seed-pic/CVH-seed-pic ./output --preview")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
