{"segmentation": {"gaussian_blur_kernel": 9, "gaussian_blur_sigma": 3.0, "threshold_method": "otsu", "manual_threshold": 127, "adaptive_method": 1, "adaptive_type": 0, "adaptive_block_size": 21, "adaptive_c": 8, "morph_kernel_size": 7, "morph_iterations": 4, "min_contour_area": 2000, "max_contour_area": 500000, "min_aspect_ratio": 0.4, "max_aspect_ratio": 3.5, "min_extent": 0.5, "min_seed_width": 80, "min_seed_height": 50, "max_seed_width": 1200, "max_seed_height": 800}, "processing": {"supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"], "create_yolo_annotations": true, "save_debug_images": true, "preview_mode": false, "max_preview_images": 5}, "description": "优化配置用于大型彩色种子图像，如CVH数据集中的椭圆形种子", "optimized_for": ["大型种子 (80-1200像素宽度)", "椭圆形种子 (纵横比 0.4-3.5)", "彩色背景上的彩色种子", "高对比度种子图像", "表面有纹理的种子"], "parameters_explanation": {"gaussian_blur_kernel": "9 - 更大的模糊核以减少种子表面纹理干扰", "gaussian_blur_sigma": "3.0 - 更强的模糊以平滑纹理", "threshold_method": "otsu - 对高对比度彩色图像效果最好", "morph_kernel_size": "7 - 更大的形态学核处理大种子", "morph_iterations": "4 - 更多迭代以清理轮廓", "min_contour_area": "2000 - 适应大种子的最小面积", "max_contour_area": "500000 - 允许非常大的种子", "min_seed_width": "80 - 适应您图像中种子的实际大小", "min_seed_height": "50 - 椭圆形种子的最小高度", "max_aspect_ratio": "3.5 - 允许较长的椭圆形种子", "min_extent": "0.5 - 确保检测到的是实心种子对象"}}