# Getting Started with Seed Classification Tool

## 🎯 Quick Start Guide

### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Test the Installation
```bash
python test_tool.py
```

### Step 3: Preview Your Data (Recommended)
```bash
python preview_tool.py "CVH-seed-pic/CVH-seed-pic" --save-preview --max-images 6
```

### Step 4: Run a Small Test
```bash
python seed_classifier.py "CVH-seed-pic/CVH-seed-pic" "./output" --preview
```

### Step 5: Full Processing
```bash
python seed_classifier.py "CVH-seed-pic/CVH-seed-pic" "./output" --debug
```

## 📁 What You'll Get

After processing, you'll have:

```
output/
├── crops/                          # Individual seed images
│   ├── species_0000002/           # Organized by species
│   │   ├── S0000002-1_seed_000.jpg
│   │   └── S0000002-1_seed_001.jpg
│   └── species_0000003/
├── annotations/                    # YOLO format files
│   ├── S0000002-1.txt             # Bounding box coordinates
│   └── S0000003-1.txt
├── debug/                         # Visualization images
├── class_mapping.json             # Species to class ID mapping
├── processing_summary.json        # Detailed report
└── processing.log                 # Processing log
```

## 🔧 Configuration

### Basic Configuration
Copy `config_template.json` to `my_config.json` and modify:

```json
{
  "segmentation": {
    "min_contour_area": 500,        // Adjust for seed size
    "threshold_method": "adaptive",  // or "otsu", "manual"
    "min_seed_width": 20,           // Minimum seed width
    "min_seed_height": 20           // Minimum seed height
  }
}
```

### Use Custom Configuration
```bash
python seed_classifier.py input_dir output_dir --config my_config.json
```

## 🎨 Preview and Tuning

The preview tool helps you optimize parameters:

```bash
python preview_tool.py "CVH-seed-pic/CVH-seed-pic" --save-preview
```

This shows:
- Original images
- Detected seed contours
- Parameter suggestions

## 📊 Understanding Results

### YOLO Annotations
Each `.txt` file contains:
```
class_id center_x center_y width height
0 0.345678 0.456789 0.123456 0.234567
```
All coordinates are normalized (0-1).

### Class Mapping
`class_mapping.json` shows:
```json
{
  "species_to_class": {"0000002": 0, "0000003": 1},
  "class_to_species": {"0": "0000002", "1": "0000003"}
}
```

### Processing Summary
`processing_summary.json` contains:
- Total images processed
- Seeds found per species
- Average seeds per image
- Configuration used

## 🛠 Troubleshooting

### No Seeds Detected
- Lower `min_contour_area` in configuration
- Try different `threshold_method`
- Use preview tool to visualize

### Too Many False Positives
- Increase `min_contour_area`
- Adjust `min_aspect_ratio` and `max_aspect_ratio`
- Enable debug images to see what's detected

### Species Not Recognized
- Check filename pattern matches `species_extraction_pattern`
- Default pattern: `S(\d+)-` extracts numbers after 'S'

## 📝 Example Workflows

### Workflow 1: First Time Use
1. `python test_tool.py` - Verify installation
2. `python preview_tool.py input_dir --save-preview` - Check segmentation
3. Adjust configuration if needed
4. `python seed_classifier.py input_dir output_dir --preview` - Small test
5. `python seed_classifier.py input_dir output_dir` - Full processing

### Workflow 2: Parameter Tuning
1. Run preview with current settings
2. Modify `config_template.json`
3. Test with `--preview` flag
4. Repeat until satisfied
5. Run full processing

### Workflow 3: Batch Processing
```python
# Use example_usage.py as template
from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig

# Process multiple directories
for input_dir in ["dataset1", "dataset2"]:
    # Configure and process
    processor = SeedProcessor(seg_config, proc_config)
    results = processor.process_batch()
```

## 🎯 Tips for Best Results

1. **Start with Preview**: Always use preview mode first
2. **Tune Parameters**: Adjust based on your seed sizes and image quality
3. **Check Debug Images**: Enable `--debug` to see detection results
4. **Validate Annotations**: Spot-check YOLO files with visualization tools
5. **Monitor Logs**: Check `processing.log` for detailed information

## 📞 Need Help?

1. Check `processing.log` for detailed error messages
2. Use preview tool to debug segmentation issues
3. Enable debug images to visualize results
4. Refer to `README.md` for detailed documentation
5. Run `python test_tool.py` to verify installation

## 🚀 Advanced Usage

See `example_usage.py` for:
- Custom configurations
- Single image analysis
- Batch processing scripts
- Configuration file usage

Happy seed processing! 🌱
