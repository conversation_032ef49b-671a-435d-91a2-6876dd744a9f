#!/usr/bin/env python3
"""
SAM种子分割工具 - GUI界面
SAM Seed Segmentation Tool - GUI Interface
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import os
import time
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageTk
import cv2
import numpy as np

# 导入后端处理模块
try:
    from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig, SAM_AVAILABLE
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"Backend import error: {e}")
    BACKEND_AVAILABLE = False

class SAMGUIApp:
    """SAM种子分割GUI应用程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("SAM种子分割工具 - SAM Seed Segmentation Tool")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 语言设置
        self.language = 'zh'  # 'zh' for Chinese, 'en' for English
        
        # 处理器实例
        self.segmenter = None
        self.processing_thread = None
        
        # GUI变量
        self.setup_variables()
        
        # 创建界面
        self.create_widgets()
        
        # 加载默认配置
        self.load_default_config()
        
        # 检查后端可用性
        if not BACKEND_AVAILABLE:
            self.log_message("❌ Backend not available. Please check sam_seed_segmenter.py", 'error')
        elif not SAM_AVAILABLE:
            self.log_message("❌ Segment Anything not installed. Please run: pip install segment-anything", 'error')
    
    def setup_variables(self):
        """设置GUI变量"""
        # 文件路径
        self.input_dir = tk.StringVar(value="CVH-seed-pic")
        self.output_dir = tk.StringVar(value="sam_output")
        self.model_path = tk.StringVar(value="sam_vit_h_4b8939.pth")
        
        # 设备设置
        self.device = tk.StringVar(value="auto")
        
        # 预览设置
        self.preview_mode = tk.BooleanVar(value=True)
        self.max_preview = tk.IntVar(value=5)
        
        # SAM参数
        self.points_per_side = tk.IntVar(value=32)
        self.pred_iou_thresh = tk.DoubleVar(value=0.88)
        self.stability_score_thresh = tk.DoubleVar(value=0.95)
        self.min_mask_region_area = tk.IntVar(value=1000)
        
        # 种子过滤参数
        self.min_seed_area = tk.IntVar(value=1000)
        self.max_seed_area = tk.IntVar(value=500000)
        self.min_aspect_ratio = tk.DoubleVar(value=0.2)
        self.max_aspect_ratio = tk.DoubleVar(value=5.0)
        self.min_solidity = tk.DoubleVar(value=0.5)
        
        # 输出设置
        self.save_debug_images = tk.BooleanVar(value=True)
        self.create_yolo_annotations = tk.BooleanVar(value=True)
        
        # 进度变量
        self.progress_var = tk.DoubleVar()
        self.current_image_var = tk.StringVar(value="Ready")
        self.processed_count_var = tk.StringVar(value="0/0")
        self.seeds_count_var = tk.StringVar(value="0")
        self.speed_var = tk.StringVar(value="0.0")
        self.eta_var = tk.StringVar(value="--:--")
        
        # 处理状态
        self.is_processing = False
        self.is_paused = False
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 文件设置标签页
        self.create_file_tab(notebook)
        
        # 参数设置标签页
        self.create_params_tab(notebook)
        
        # 处理控制标签页
        self.create_control_tab(notebook)
        
        # 结果预览标签页
        self.create_preview_tab(notebook)
    
    def create_file_tab(self, parent):
        """创建文件设置标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="文件设置 / File Settings")
        
        # 输入目录
        ttk.Label(frame, text="输入图像目录 / Input Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(frame, text="浏览 / Browse", command=self.browse_input_dir).grid(row=0, column=2, padx=5, pady=5)
        
        # 输出目录
        ttk.Label(frame, text="输出目录 / Output Directory:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(frame, text="浏览 / Browse", command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=5)
        
        # SAM模型文件
        ttk.Label(frame, text="SAM模型文件 / SAM Model File:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.model_path, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(frame, text="浏览 / Browse", command=self.browse_model_file).grid(row=2, column=2, padx=5, pady=5)
        
        # 设备选择
        ttk.Label(frame, text="计算设备 / Device:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        device_combo = ttk.Combobox(frame, textvariable=self.device, values=["auto", "cpu", "cuda"], state="readonly")
        device_combo.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 预览设置
        preview_frame = ttk.LabelFrame(frame, text="预览设置 / Preview Settings")
        preview_frame.grid(row=4, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=10)
        
        ttk.Checkbutton(preview_frame, text="预览模式 / Preview Mode", variable=self.preview_mode).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(preview_frame, text="最大预览图像数 / Max Preview Images:").grid(row=0, column=1, padx=5, pady=5)
        ttk.Spinbox(preview_frame, from_=1, to=20, textvariable=self.max_preview, width=10).grid(row=0, column=2, padx=5, pady=5)
        
        # 输出选项
        output_frame = ttk.LabelFrame(frame, text="输出选项 / Output Options")
        output_frame.grid(row=5, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=10)
        
        ttk.Checkbutton(output_frame, text="保存调试图像 / Save Debug Images", variable=self.save_debug_images).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(output_frame, text="创建YOLO注释 / Create YOLO Annotations", variable=self.create_yolo_annotations).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 配置文件操作
        config_frame = ttk.LabelFrame(frame, text="配置文件 / Configuration")
        config_frame.grid(row=6, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=10)
        
        ttk.Button(config_frame, text="保存配置 / Save Config", command=self.save_config).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(config_frame, text="加载配置 / Load Config", command=self.load_config).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(config_frame, text="重置配置 / Reset Config", command=self.reset_config).grid(row=0, column=2, padx=5, pady=5)
    
    def create_params_tab(self, parent):
        """创建参数设置标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="参数设置 / Parameters")
        
        # SAM参数
        sam_frame = ttk.LabelFrame(frame, text="SAM模型参数 / SAM Model Parameters")
        sam_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Points per side
        ttk.Label(sam_frame, text="采样点密度 / Points per Side:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(sam_frame, from_=8, to=64, orient=tk.HORIZONTAL, variable=self.points_per_side, length=200).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(sam_frame, textvariable=self.points_per_side).grid(row=0, column=2, padx=5, pady=5)
        
        # IoU threshold
        ttk.Label(sam_frame, text="IoU阈值 / IoU Threshold:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(sam_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.pred_iou_thresh, length=200).grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(sam_frame, textvariable=self.pred_iou_thresh).grid(row=1, column=2, padx=5, pady=5)
        
        # Stability threshold
        ttk.Label(sam_frame, text="稳定性阈值 / Stability Threshold:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(sam_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.stability_score_thresh, length=200).grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(sam_frame, textvariable=self.stability_score_thresh).grid(row=2, column=2, padx=5, pady=5)
        
        # 种子过滤参数
        filter_frame = ttk.LabelFrame(frame, text="种子过滤参数 / Seed Filtering Parameters")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 面积范围
        ttk.Label(filter_frame, text="最小种子面积 / Min Seed Area:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.min_seed_area, width=15).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="最大种子面积 / Max Seed Area:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.max_seed_area, width=15).grid(row=0, column=3, padx=5, pady=5)
        
        # 纵横比范围
        ttk.Label(filter_frame, text="最小纵横比 / Min Aspect Ratio:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.min_aspect_ratio, width=15).grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="最大纵横比 / Max Aspect Ratio:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.max_aspect_ratio, width=15).grid(row=1, column=3, padx=5, pady=5)
        
        # 凸度
        ttk.Label(filter_frame, text="最小凸度 / Min Solidity:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.min_solidity, width=15).grid(row=2, column=1, padx=5, pady=5)
        
        # 参数说明
        help_frame = ttk.LabelFrame(frame, text="参数说明 / Parameter Help")
        help_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        help_text = """
参数说明 / Parameter Descriptions:

• 采样点密度 (Points per Side): 控制分割精度，值越大越精细但速度越慢
• IoU阈值 (IoU Threshold): 控制掩码质量，值越高质量越好但可能遗漏对象
• 稳定性阈值 (Stability Threshold): 过滤不稳定的分割结果
• 种子面积 (Seed Area): 过滤太小或太大的检测结果
• 纵横比 (Aspect Ratio): 控制种子形状，适应椭圆形种子
• 凸度 (Solidity): 确保检测到的是实心种子对象

推荐设置 / Recommended Settings:
- 高精度: Points=32, IoU=0.9, Stability=0.95
- 平衡模式: Points=24, IoU=0.88, Stability=0.9
- 快速模式: Points=16, IoU=0.85, Stability=0.85
        """
        
        help_text_widget = scrolledtext.ScrolledText(help_frame, height=10, wrap=tk.WORD)
        help_text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)

    def create_control_tab(self, parent):
        """创建处理控制标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="处理控制 / Processing Control")

        # 控制按钮
        control_frame = ttk.LabelFrame(frame, text="处理控制 / Processing Control")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_button = ttk.Button(control_frame, text="开始处理 / Start Processing", command=self.start_processing)
        self.start_button.grid(row=0, column=0, padx=5, pady=5)

        self.pause_button = ttk.Button(control_frame, text="暂停 / Pause", command=self.pause_processing, state=tk.DISABLED)
        self.pause_button.grid(row=0, column=1, padx=5, pady=5)

        self.stop_button = ttk.Button(control_frame, text="停止 / Stop", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=2, padx=5, pady=5)

        # 进度显示
        progress_frame = ttk.LabelFrame(frame, text="处理进度 / Processing Progress")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        # 总体进度条
        ttk.Label(progress_frame, text="总体进度 / Overall Progress:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=400)
        self.progress_bar.grid(row=0, column=1, columnspan=2, padx=5, pady=5, sticky=tk.EW)

        # 当前图像
        ttk.Label(progress_frame, text="当前图像 / Current Image:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(progress_frame, textvariable=self.current_image_var, foreground="blue").grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 处理统计
        stats_frame = ttk.LabelFrame(frame, text="处理统计 / Processing Statistics")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(stats_frame, text="已处理/总数 / Processed/Total:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.processed_count_var, foreground="green").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(stats_frame, text="检测到种子数 / Seeds Detected:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.seeds_count_var, foreground="orange").grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(stats_frame, text="处理速度 / Speed (img/s):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.speed_var, foreground="purple").grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(stats_frame, text="预计剩余时间 / ETA:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.eta_var, foreground="red").grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)

        # 处理日志
        log_frame = ttk.LabelFrame(frame, text="处理日志 / Processing Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 日志控制
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_control_frame, text="清空日志 / Clear Log", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="保存日志 / Save Log", command=self.save_log).pack(side=tk.LEFT, padx=5)

    def create_preview_tab(self, parent):
        """创建结果预览标签页"""
        frame = ttk.Frame(parent)
        parent.add(frame, text="结果预览 / Preview")

        # 图像预览
        preview_frame = ttk.LabelFrame(frame, text="图像预览 / Image Preview")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建画布用于显示图像
        self.preview_canvas = tk.Canvas(preview_frame, bg="white", width=600, height=400)
        self.preview_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 预览控制
        preview_control_frame = ttk.Frame(preview_frame)
        preview_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(preview_control_frame, text="刷新预览 / Refresh Preview", command=self.refresh_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(preview_control_frame, text="打开输出目录 / Open Output Dir", command=self.open_output_dir).pack(side=tk.LEFT, padx=5)

        # 结果统计
        result_frame = ttk.LabelFrame(frame, text="处理结果 / Processing Results")
        result_frame.pack(fill=tk.X, padx=5, pady=5)

        self.result_text = scrolledtext.ScrolledText(result_frame, height=8, wrap=tk.WORD)
        self.result_text.pack(fill=tk.X, padx=5, pady=5)

    # 文件操作方法
    def browse_input_dir(self):
        """浏览输入目录"""
        directory = filedialog.askdirectory(title="选择输入图像目录 / Select Input Directory")
        if directory:
            self.input_dir.set(directory)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录 / Select Output Directory")
        if directory:
            self.output_dir.set(directory)

    def browse_model_file(self):
        """浏览模型文件"""
        filename = filedialog.askopenfilename(
            title="选择SAM模型文件 / Select SAM Model File",
            filetypes=[("PyTorch模型文件", "*.pth"), ("所有文件", "*.*")]
        )
        if filename:
            self.model_path.set(filename)

    # 配置文件操作
    def save_config(self):
        """保存配置"""
        config = self.get_current_config()
        filename = filedialog.asksaveasfilename(
            title="保存配置文件 / Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                self.log_message(f"配置已保存到 / Configuration saved to: {filename}")
            except Exception as e:
                messagebox.showerror("错误 / Error", f"保存配置失败 / Failed to save config: {e}")

    def load_config(self):
        """加载配置"""
        filename = filedialog.askopenfilename(
            title="加载配置文件 / Load Configuration",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.set_config(config)
                self.log_message(f"配置已加载 / Configuration loaded from: {filename}")
            except Exception as e:
                messagebox.showerror("错误 / Error", f"加载配置失败 / Failed to load config: {e}")

    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认 / Confirm", "确定要重置所有配置吗？ / Reset all configurations?"):
            self.load_default_config()
            self.log_message("配置已重置为默认值 / Configuration reset to defaults")

    def load_default_config(self):
        """加载默认配置"""
        # 设置默认值（已在setup_variables中设置）
        pass

    def get_current_config(self):
        """获取当前配置"""
        return {
            "file_settings": {
                "input_dir": self.input_dir.get(),
                "output_dir": self.output_dir.get(),
                "model_path": self.model_path.get(),
                "device": self.device.get()
            },
            "preview_settings": {
                "preview_mode": self.preview_mode.get(),
                "max_preview": self.max_preview.get()
            },
            "sam_parameters": {
                "points_per_side": self.points_per_side.get(),
                "pred_iou_thresh": self.pred_iou_thresh.get(),
                "stability_score_thresh": self.stability_score_thresh.get(),
                "min_mask_region_area": self.min_mask_region_area.get()
            },
            "seed_filtering": {
                "min_seed_area": self.min_seed_area.get(),
                "max_seed_area": self.max_seed_area.get(),
                "min_aspect_ratio": self.min_aspect_ratio.get(),
                "max_aspect_ratio": self.max_aspect_ratio.get(),
                "min_solidity": self.min_solidity.get()
            },
            "output_settings": {
                "save_debug_images": self.save_debug_images.get(),
                "create_yolo_annotations": self.create_yolo_annotations.get()
            }
        }

    def set_config(self, config):
        """设置配置"""
        try:
            # 文件设置
            if "file_settings" in config:
                fs = config["file_settings"]
                self.input_dir.set(fs.get("input_dir", ""))
                self.output_dir.set(fs.get("output_dir", ""))
                self.model_path.set(fs.get("model_path", ""))
                self.device.set(fs.get("device", "auto"))

            # 预览设置
            if "preview_settings" in config:
                ps = config["preview_settings"]
                self.preview_mode.set(ps.get("preview_mode", True))
                self.max_preview.set(ps.get("max_preview", 5))

            # SAM参数
            if "sam_parameters" in config:
                sp = config["sam_parameters"]
                self.points_per_side.set(sp.get("points_per_side", 32))
                self.pred_iou_thresh.set(sp.get("pred_iou_thresh", 0.88))
                self.stability_score_thresh.set(sp.get("stability_score_thresh", 0.95))
                self.min_mask_region_area.set(sp.get("min_mask_region_area", 1000))

            # 种子过滤
            if "seed_filtering" in config:
                sf = config["seed_filtering"]
                self.min_seed_area.set(sf.get("min_seed_area", 1000))
                self.max_seed_area.set(sf.get("max_seed_area", 500000))
                self.min_aspect_ratio.set(sf.get("min_aspect_ratio", 0.2))
                self.max_aspect_ratio.set(sf.get("max_aspect_ratio", 5.0))
                self.min_solidity.set(sf.get("min_solidity", 0.5))

            # 输出设置
            if "output_settings" in config:
                os_config = config["output_settings"]
                self.save_debug_images.set(os_config.get("save_debug_images", True))
                self.create_yolo_annotations.set(os_config.get("create_yolo_annotations", True))

        except Exception as e:
            self.log_message(f"设置配置时出错 / Error setting config: {e}", 'error')

    # 处理控制方法
    def start_processing(self):
        """开始处理"""
        if not self.validate_inputs():
            return

        if self.is_processing:
            self.log_message("处理已在进行中 / Processing already in progress", 'warning')
            return

        # 更新按钮状态
        self.start_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL)

        self.is_processing = True
        self.is_paused = False

        # 重置进度
        self.progress_var.set(0)
        self.current_image_var.set("Initializing...")
        self.processed_count_var.set("0/0")
        self.seeds_count_var.set("0")

        # 在新线程中开始处理
        self.processing_thread = threading.Thread(target=self._process_images, daemon=True)
        self.processing_thread.start()

        self.log_message("开始处理图像 / Started image processing")

    def pause_processing(self):
        """暂停/恢复处理"""
        if not self.is_processing:
            return

        if self.is_paused:
            # 恢复处理
            self.is_paused = False
            if self.segmenter:
                self.segmenter.resume_processing()
            self.pause_button.config(text="暂停 / Pause")
            self.log_message("恢复处理 / Resumed processing")
        else:
            # 暂停处理
            self.is_paused = True
            if self.segmenter:
                self.segmenter.pause_processing()
            self.pause_button.config(text="恢复 / Resume")
            self.log_message("暂停处理 / Paused processing")

    def stop_processing(self):
        """停止处理"""
        if not self.is_processing:
            return

        if self.segmenter:
            self.segmenter.stop_processing()

        self.is_processing = False
        self.is_paused = False

        # 更新按钮状态
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="暂停 / Pause")
        self.stop_button.config(state=tk.DISABLED)

        self.current_image_var.set("Stopped")
        self.log_message("停止处理 / Stopped processing")

    def validate_inputs(self):
        """验证输入"""
        # 检查输入目录
        if not os.path.exists(self.input_dir.get()):
            messagebox.showerror("错误 / Error", "输入目录不存在 / Input directory does not exist")
            return False

        # 检查模型文件
        if not os.path.exists(self.model_path.get()):
            messagebox.showerror("错误 / Error", "SAM模型文件不存在 / SAM model file does not exist")
            return False

        # 检查后端可用性
        if not BACKEND_AVAILABLE:
            messagebox.showerror("错误 / Error", "后端模块不可用 / Backend module not available")
            return False

        if not SAM_AVAILABLE:
            messagebox.showerror("错误 / Error", "Segment Anything未安装 / Segment Anything not installed")
            return False

        return True

    def _process_images(self):
        """在后台线程中处理图像"""
        try:
            # 创建SAM配置
            sam_config = SAMConfig(
                model_type="vit_h",
                checkpoint_path=self.model_path.get(),
                device=self.device.get(),
                points_per_side=self.points_per_side.get(),
                pred_iou_thresh=self.pred_iou_thresh.get(),
                stability_score_thresh=self.stability_score_thresh.get(),
                min_mask_region_area=self.min_mask_region_area.get()
            )

            # 创建处理配置
            processing_config = ProcessingConfig(
                input_directory=self.input_dir.get(),
                output_directory=self.output_dir.get(),
                preview_mode=self.preview_mode.get(),
                max_preview_images=self.max_preview.get(),
                save_debug_images=self.save_debug_images.get(),
                create_yolo_annotations=self.create_yolo_annotations.get(),
                min_seed_area=self.min_seed_area.get(),
                max_seed_area=self.max_seed_area.get(),
                min_aspect_ratio=self.min_aspect_ratio.get(),
                max_aspect_ratio=self.max_aspect_ratio.get(),
                min_solidity=self.min_solidity.get()
            )

            # 创建分割器
            self.segmenter = SAMSeedSegmenter(
                sam_config,
                processing_config,
                progress_callback=self._update_progress_callback,
                log_callback=self._log_callback
            )

            # 开始处理
            start_time = time.time()
            results = self.segmenter.process_directory()
            end_time = time.time()

            # 处理完成
            self.root.after(0, self._processing_completed, results, end_time - start_time)

        except Exception as e:
            self.root.after(0, self._processing_error, str(e))

    def _update_progress_callback(self, progress_data):
        """进度更新回调"""
        def update_gui():
            self.progress_var.set(progress_data['percentage'])
            self.current_image_var.set(progress_data['current_image'])
            self.processed_count_var.set(f"{progress_data['current']}/{progress_data['total']}")
            self.seeds_count_var.set(str(progress_data['total_seeds']))

            # 计算处理速度和ETA
            if progress_data['current'] > 0:
                elapsed_time = time.time() - getattr(self, '_start_time', time.time())
                speed = progress_data['current'] / elapsed_time if elapsed_time > 0 else 0
                self.speed_var.set(f"{speed:.2f}")

                if speed > 0 and progress_data['current'] < progress_data['total']:
                    remaining_images = progress_data['total'] - progress_data['current']
                    eta_seconds = remaining_images / speed
                    eta_minutes = int(eta_seconds // 60)
                    eta_seconds = int(eta_seconds % 60)
                    self.eta_var.set(f"{eta_minutes:02d}:{eta_seconds:02d}")
                else:
                    self.eta_var.set("--:--")

        self.root.after(0, update_gui)

    def _log_callback(self, message, level='info'):
        """日志回调"""
        def update_log():
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}\n"

            self.log_text.insert(tk.END, formatted_message)
            self.log_text.see(tk.END)

            # 根据日志级别设置颜色
            if level == 'error':
                # 设置错误消息为红色
                start_line = self.log_text.index(tk.END + "-2l")
                end_line = self.log_text.index(tk.END + "-1l")
                self.log_text.tag_add("error", start_line, end_line)
                self.log_text.tag_config("error", foreground="red")
            elif level == 'warning':
                # 设置警告消息为橙色
                start_line = self.log_text.index(tk.END + "-2l")
                end_line = self.log_text.index(tk.END + "-1l")
                self.log_text.tag_add("warning", start_line, end_line)
                self.log_text.tag_config("warning", foreground="orange")

        self.root.after(0, update_log)

    def _processing_completed(self, results, processing_time):
        """处理完成回调"""
        self.is_processing = False

        # 更新按钮状态
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="暂停 / Pause")
        self.stop_button.config(state=tk.DISABLED)

        # 显示结果
        self.current_image_var.set("Completed")
        self.progress_var.set(100)

        # 更新结果文本
        result_summary = f"""
处理完成 / Processing Completed!
================================

总处理时间 / Total Time: {processing_time:.1f} 秒
处理图像数 / Images Processed: {results['total_images']}
成功处理数 / Successfully Processed: {results['successful_images']}
检测到种子总数 / Total Seeds Detected: {results['total_seeds_found']}
平均处理速度 / Average Speed: {results['total_images']/processing_time:.2f} 图像/秒

输出目录 / Output Directory: {self.output_dir.get()}
- crops/ : 种子裁剪图像
- debug/ : 调试可视化图像
- annotations/ : YOLO格式注释文件
        """

        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, result_summary)

        self.log_message("处理完成! / Processing completed!")

        # 显示完成对话框
        messagebox.showinfo(
            "完成 / Completed",
            f"处理完成!\n检测到 {results['total_seeds_found']} 个种子\n输出保存在: {self.output_dir.get()}"
        )

    def _processing_error(self, error_message):
        """处理错误回调"""
        self.is_processing = False

        # 更新按钮状态
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="暂停 / Pause")
        self.stop_button.config(state=tk.DISABLED)

        self.current_image_var.set("Error")
        self.log_message(f"处理错误 / Processing error: {error_message}", 'error')

        messagebox.showerror("错误 / Error", f"处理过程中发生错误:\n{error_message}")

    # 日志和预览方法
    def log_message(self, message, level='info'):
        """添加日志消息"""
        self._log_callback(message, level)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志 / Save Log",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"日志已保存到 / Log saved to: {filename}")
            except Exception as e:
                messagebox.showerror("错误 / Error", f"保存日志失败 / Failed to save log: {e}")

    def refresh_preview(self):
        """刷新预览"""
        # 这里可以添加预览图像的代码
        self.log_message("预览功能开发中 / Preview feature under development")

    def open_output_dir(self):
        """打开输出目录"""
        output_path = self.output_dir.get()
        if os.path.exists(output_path):
            if os.name == 'nt':  # Windows
                os.startfile(output_path)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{output_path}"' if sys.platform == 'darwin' else f'xdg-open "{output_path}"')
        else:
            messagebox.showwarning("警告 / Warning", "输出目录不存在 / Output directory does not exist")

def main():
    """主函数"""
    root = tk.Tk()
    app = SAMGUIApp(root)

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass

    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()
