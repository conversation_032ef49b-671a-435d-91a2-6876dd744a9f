# Seed Classification Tool

A comprehensive Python tool for automatic seed segmentation, classification, and YOLO annotation generation from seed images.

## Features

- **Automatic Seed Segmentation**: Uses advanced computer vision techniques (contour detection, morphological operations, adaptive thresholding) to automatically detect and extract individual seeds
- **Species Organization**: Automatically organizes extracted seeds into species-specific directories based on filename patterns
- **YOLO Annotations**: Generates YOLO format annotation files with bounding box coordinates and class labels for object detection training
- **Quality Control**: Implements filtering based on size, shape, and other criteria to ensure high-quality segmentation
- **Preview Mode**: Allows you to preview segmentation results before processing large batches
- **Configurable Parameters**: Extensive configuration options for fine-tuning segmentation performance
- **Comprehensive Logging**: Detailed logging and progress tracking for batch processing
- **Summary Reports**: Generates detailed processing reports with statistics

## Installation

1. Clone or download the tool files
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Quick Start

### 1. Preview Mode (Recommended First Step)

Before processing your entire dataset, use preview mode to test and tune parameters:

```bash
python preview_tool.py "CVH-seed-pic/CVH-seed-pic" --max-images 6 --save-preview
```

This will:
- Process the first 6 images
- Show segmentation results
- Save a preview image
- Display parameter tuning suggestions

### 2. Basic Usage

Process all images with default settings:

```bash
python seed_classifier.py "CVH-seed-pic/CVH-seed-pic" "./output"
```

### 3. Preview Before Full Processing

Test with a few images first:

```bash
python seed_classifier.py "CVH-seed-pic/CVH-seed-pic" "./output" --preview
```

### 4. Full Processing with Debug Images

```bash
python seed_classifier.py "CVH-seed-pic/CVH-seed-pic" "./output" --debug
```

## Configuration

### Using Configuration Files

Create a custom configuration file based on `config_template.json`:

```bash
python seed_classifier.py "CVH-seed-pic/CVH-seed-pic" "./output" --config my_config.json
```

### Key Configuration Parameters

#### Segmentation Parameters
- `threshold_method`: "adaptive", "otsu", or "manual"
- `min_contour_area`: Minimum seed area in pixels (default: 500)
- `max_contour_area`: Maximum seed area in pixels (default: 50000)
- `min_seed_width/height`: Minimum seed dimensions (default: 20x20)
- `max_seed_width/height`: Maximum seed dimensions (default: 500x500)

#### Processing Parameters
- `species_extraction_pattern`: Regex pattern to extract species ID from filename
- `create_yolo_annotations`: Generate YOLO format annotations (default: true)
- `save_debug_images`: Save images showing detected contours (default: false)

## Output Structure

The tool creates the following output structure:

```
output/
├── crops/                          # Individual seed images
│   ├── species_0000002/           # Species-specific directories
│   │   ├── S0000002-1_seed_000.jpg
│   │   ├── S0000002-1_seed_001.jpg
│   │   └── ...
│   └── species_0000003/
│       └── ...
├── annotations/                    # YOLO format annotations
│   ├── S0000002-1.txt
│   ├── S0000003-1.txt
│   └── ...
├── debug/                         # Debug images (if enabled)
│   ├── debug_S0000002-1.jpg
│   └── ...
├── class_mapping.json             # Species ID to class mapping
├── processing_summary.json        # Detailed processing report
└── processing.log                 # Processing log file
```

## YOLO Annotation Format

Each annotation file contains one line per detected seed:
```
class_id center_x center_y width height
```

All coordinates are normalized (0-1 range).

## Parameter Tuning Guide

### If too many small objects are detected:
- Increase `min_contour_area`
- Increase `min_seed_width` and `min_seed_height`

### If seeds are missed:
- Decrease `min_contour_area`
- Adjust threshold parameters
- Check `min_extent` value

### If background noise is detected:
- Use "adaptive" threshold method
- Adjust `adaptive_block_size` and `adaptive_c`
- Increase morphological operation iterations

### For different seed shapes:
- Adjust `min_aspect_ratio` and `max_aspect_ratio`
- Modify `min_extent` for irregular shapes

## Species ID Extraction

By default, the tool extracts species IDs using the pattern `S(\d+)-` from filenames.
For example:
- `S0000002-1.jpg` → Species ID: `0000002`
- `S0000123-1.jpg` → Species ID: `0000123`

Customize the pattern in configuration if your filenames follow a different format.

## Advanced Usage

### Custom Configuration Example

```json
{
  "segmentation": {
    "threshold_method": "adaptive",
    "min_contour_area": 800,
    "max_contour_area": 30000,
    "min_seed_width": 25,
    "min_seed_height": 25,
    "adaptive_block_size": 15,
    "adaptive_c": 3
  },
  "processing": {
    "species_extraction_pattern": "SEED_(\\d+)_",
    "save_debug_images": true
  }
}
```

### Batch Processing Script

For processing multiple directories:

```python
import os
from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig

# Define directories
input_dirs = ["dataset1", "dataset2", "dataset3"]
output_base = "./processed_seeds"

for input_dir in input_dirs:
    output_dir = os.path.join(output_base, os.path.basename(input_dir))
    
    # Configure
    seg_config = SegmentationConfig()
    proc_config = ProcessingConfig(
        input_directory=input_dir,
        output_directory=output_dir
    )
    
    # Process
    processor = SeedProcessor(seg_config, proc_config)
    results = processor.process_batch()
    
    print(f"Processed {input_dir}: {results['summary']['total_seeds_extracted']} seeds")
```

## Troubleshooting

### Common Issues

1. **No seeds detected**: 
   - Check if images are loading correctly
   - Try lowering `min_contour_area`
   - Use preview mode to visualize results

2. **Too many false positives**:
   - Increase `min_contour_area`
   - Adjust aspect ratio constraints
   - Enable debug images to see what's being detected

3. **Memory issues with large images**:
   - Resize images before processing
   - Process in smaller batches

4. **Species ID not extracted**:
   - Check `species_extraction_pattern` in configuration
   - Verify filename format matches the regex pattern

### Performance Tips

- Use preview mode to optimize parameters before full processing
- Enable debug images only when needed (they consume disk space)
- Process images in batches for very large datasets
- Consider resizing very large images for faster processing

## Support

For issues or questions:
1. Check the processing log file for detailed error messages
2. Use preview mode to debug segmentation issues
3. Adjust configuration parameters based on your specific dataset
4. Enable debug images to visualize detection results

## License

This tool is provided as-is for research and educational purposes.
