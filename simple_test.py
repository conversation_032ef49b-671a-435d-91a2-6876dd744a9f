import cv2
import numpy as np
from pathlib import Path
import os

# Test basic functionality
print("Testing OpenCV...")
try:
    # Create a simple test image
    test_img = np.zeros((100, 100, 3), dtype=np.uint8)
    test_img[25:75, 25:75] = [255, 255, 255]
    
    # Test OpenCV operations
    gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
    print("✓ OpenCV working")
    
    # Test file operations
    Path("test_output").mkdir(exist_ok=True)
    cv2.imwrite("test_output/test.jpg", test_img)
    print("✓ File writing working")
    
    # Check input directory
    input_dir = Path("CVH-seed-pic/CVH-seed-pic")
    if input_dir.exists():
        jpg_files = list(input_dir.glob("*.jpg"))
        print(f"✓ Found {len(jpg_files)} JPG files")
        if jpg_files:
            print(f"  First file: {jpg_files[0].name}")
    else:
        print("✗ Input directory not found")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
