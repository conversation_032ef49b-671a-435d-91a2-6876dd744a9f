#!/usr/bin/env python3
"""
SAM Seed Segmentation Tool - Advanced GUI with Enhanced Features
SAM种子分割工具 - 增强功能的高级GUI

Features:
- Language toggle system
- Session-based output organization
- Enhanced results preview system
- Integrated YOLO training module
- YOLO detection and visualization
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import os
import time
import sys
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageTk
import cv2
import numpy as np
import shutil
import subprocess

# Import backend modules
try:
    from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig, SAM_AVAILABLE
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"Backend import error: {e}")
    BACKEND_AVAILABLE = False

class SessionManager:
    """Session management for organizing processing results"""
    
    def __init__(self, base_output_dir):
        self.base_output_dir = Path(base_output_dir)
        self.current_session_id = None
        self.current_session_dir = None
        
    def create_new_session(self):
        """Create a new processing session"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.current_session_id = f"session_{timestamp}"
        self.current_session_dir = self.base_output_dir / self.current_session_id
        
        # Create session directory structure
        session_dirs = ['crops', 'annotations', 'debug', 'logs', 'reports']
        for dir_name in session_dirs:
            (self.current_session_dir / dir_name).mkdir(parents=True, exist_ok=True)
        
        return self.current_session_id, self.current_session_dir
    
    def get_session_list(self):
        """Get list of existing sessions"""
        if not self.base_output_dir.exists():
            return []
        
        sessions = []
        for item in self.base_output_dir.iterdir():
            if item.is_dir() and item.name.startswith('session_'):
                sessions.append(item.name)
        
        return sorted(sessions, reverse=True)  # Most recent first
    
    def load_session(self, session_id):
        """Load an existing session"""
        self.current_session_id = session_id
        self.current_session_dir = self.base_output_dir / session_id
        return self.current_session_dir

class LanguageManager:
    """Language management for GUI text"""
    
    def __init__(self, config_file="sam_gui_config.json"):
        self.config_file = config_file
        self.current_language = "en"
        self.languages = {}
        self.tooltips = {}
        self.load_languages()
    
    def load_languages(self):
        """Load language definitions from config"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.languages = config.get('languages', {})
            self.tooltips = config.get('tooltips', {})
            self.current_language = config.get('gui_settings', {}).get('language', 'en')
        except Exception as e:
            print(f"Error loading languages: {e}")
            # Fallback to basic English
            self.languages = {
                'en': {
                    'app_title': 'SAM Seed Segmentation Tool',
                    'file_settings': 'File Settings',
                    'parameters': 'Parameters',
                    'processing_control': 'Processing Control',
                    'preview': 'Results Preview'
                }
            }
    
    def get_text(self, key, default=None):
        """Get text in current language"""
        if default is None:
            default = key
        
        return self.languages.get(self.current_language, {}).get(key, default)
    
    def get_tooltip(self, key, default=""):
        """Get tooltip in current language"""
        return self.tooltips.get(self.current_language, {}).get(key, default)
    
    def set_language(self, language):
        """Set current language"""
        if language in self.languages:
            self.current_language = language
            self.save_language_preference()
    
    def save_language_preference(self):
        """Save language preference to config"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            config.setdefault('gui_settings', {})['language'] = self.current_language
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving language preference: {e}")

class ImagePreviewWidget:
    """Enhanced image preview widget with navigation"""
    
    def __init__(self, parent, width=800, height=600):
        self.parent = parent
        self.width = width
        self.height = height
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Image list and current index
        self.image_paths = []
        self.current_index = 0
        self.current_image = None
        self.zoom_factor = 1.0
        
        # Create widgets
        self.create_widgets()
        
        # Bind keyboard events
        self.setup_keyboard_bindings()
    
    def create_widgets(self):
        """Create preview widgets"""
        # Navigation frame
        nav_frame = ttk.Frame(self.frame)
        nav_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.prev_button = ttk.Button(nav_frame, text="◀ Previous", command=self.previous_image)
        self.prev_button.pack(side=tk.LEFT, padx=5)
        
        self.next_button = ttk.Button(nav_frame, text="Next ▶", command=self.next_image)
        self.next_button.pack(side=tk.LEFT, padx=5)
        
        self.image_info_label = ttk.Label(nav_frame, text="No images loaded")
        self.image_info_label.pack(side=tk.LEFT, padx=20)
        
        # Zoom controls
        zoom_frame = ttk.Frame(nav_frame)
        zoom_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(zoom_frame, text="Zoom In", command=self.zoom_in).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Zoom Out", command=self.zoom_out).pack(side=tk.LEFT, padx=2)
        ttk.Button(zoom_frame, text="Fit", command=self.fit_to_window).pack(side=tk.LEFT, padx=2)
        
        # Canvas for image display
        canvas_frame = ttk.Frame(self.frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.canvas = tk.Canvas(canvas_frame, bg="white", width=self.width, height=self.height)
        
        # Scrollbars
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # Pack scrollbars and canvas
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    def setup_keyboard_bindings(self):
        """Setup keyboard navigation"""
        self.canvas.bind("<Key>", self.on_key_press)
        self.canvas.focus_set()
    
    def on_key_press(self, event):
        """Handle keyboard navigation"""
        if event.keysym in ['a', 'A', 'Left']:
            self.previous_image()
        elif event.keysym in ['d', 'D', 'Right']:
            self.next_image()
        elif event.keysym in ['Up']:
            self.zoom_in()
        elif event.keysym in ['Down']:
            self.zoom_out()
    
    def load_images(self, image_paths):
        """Load list of images"""
        self.image_paths = list(image_paths)
        self.current_index = 0
        if self.image_paths:
            self.display_current_image()
        self.update_navigation()
    
    def display_current_image(self):
        """Display current image"""
        if not self.image_paths or self.current_index >= len(self.image_paths):
            return
        
        try:
            image_path = self.image_paths[self.current_index]
            
            # Load image
            pil_image = Image.open(image_path)
            
            # Apply zoom
            if self.zoom_factor != 1.0:
                new_size = (int(pil_image.width * self.zoom_factor), 
                           int(pil_image.height * self.zoom_factor))
                pil_image = pil_image.resize(new_size, Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            self.current_image = ImageTk.PhotoImage(pil_image)
            
            # Clear canvas and display image
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.current_image)
            
            # Update scroll region
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
            # Update info label
            filename = Path(image_path).name
            self.image_info_label.config(text=f"{filename} ({self.current_index + 1}/{len(self.image_paths)})")
            
        except Exception as e:
            print(f"Error displaying image: {e}")
            self.image_info_label.config(text=f"Error loading image: {e}")
    
    def previous_image(self):
        """Go to previous image"""
        if self.image_paths and self.current_index > 0:
            self.current_index -= 1
            self.display_current_image()
        self.update_navigation()
    
    def next_image(self):
        """Go to next image"""
        if self.image_paths and self.current_index < len(self.image_paths) - 1:
            self.current_index += 1
            self.display_current_image()
        self.update_navigation()
    
    def zoom_in(self):
        """Zoom in"""
        self.zoom_factor *= 1.2
        self.display_current_image()
    
    def zoom_out(self):
        """Zoom out"""
        self.zoom_factor /= 1.2
        self.display_current_image()
    
    def fit_to_window(self):
        """Fit image to window"""
        self.zoom_factor = 1.0
        self.display_current_image()
    
    def update_navigation(self):
        """Update navigation button states"""
        self.prev_button.config(state=tk.NORMAL if self.current_index > 0 else tk.DISABLED)
        self.next_button.config(state=tk.NORMAL if self.current_index < len(self.image_paths) - 1 else tk.DISABLED)
    
    def pack(self, **kwargs):
        """Pack the widget"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """Grid the widget"""
        self.frame.grid(**kwargs)

class YOLOTrainingModule:
    """YOLO training module for the GUI"""

    def __init__(self, parent, session_manager, language_manager):
        self.parent = parent
        self.session_manager = session_manager
        self.lang_mgr = language_manager

        # Training variables
        self.training_thread = None
        self.is_training = False

        # Create widgets
        self.create_widgets()

    def create_widgets(self):
        """Create YOLO training widgets"""
        # Main frame
        self.frame = ttk.Frame(self.parent)

        # Session selection
        session_frame = ttk.LabelFrame(self.frame, text="Session Selection")
        session_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(session_frame, text="Training Session:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.session_combo = ttk.Combobox(session_frame, state="readonly", width=30)
        self.session_combo.grid(row=0, column=1, padx=5, pady=5)
        self.session_combo.bind("<<ComboboxSelected>>", self.on_session_selected)

        ttk.Button(session_frame, text="Refresh Sessions", command=self.refresh_sessions).grid(row=0, column=2, padx=5, pady=5)

        # Data validation
        validation_frame = ttk.LabelFrame(self.frame, text="Data Validation")
        validation_frame.pack(fill=tk.X, padx=5, pady=5)

        self.validation_text = scrolledtext.ScrolledText(validation_frame, height=6, wrap=tk.WORD)
        self.validation_text.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(validation_frame, text="Validate Data", command=self.validate_training_data).pack(pady=5)

        # Training configuration
        config_frame = ttk.LabelFrame(self.frame, text="Training Configuration")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # Model selection
        ttk.Label(config_frame, text="YOLO Model:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.model_combo = ttk.Combobox(config_frame, values=["yolov8n", "yolov8s", "yolov8m", "yolov8l", "yolov8x"], state="readonly")
        self.model_combo.set("yolov8n")
        self.model_combo.grid(row=0, column=1, padx=5, pady=5)

        # Training parameters
        ttk.Label(config_frame, text="Epochs:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.epochs_var = tk.IntVar(value=100)
        ttk.Spinbox(config_frame, from_=10, to=1000, textvariable=self.epochs_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(config_frame, text="Batch Size:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.batch_size_var = tk.IntVar(value=16)
        ttk.Spinbox(config_frame, from_=1, to=64, textvariable=self.batch_size_var, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(config_frame, text="Image Size:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.img_size_var = tk.IntVar(value=640)
        ttk.Combobox(config_frame, values=[320, 416, 512, 640, 832, 1024], textvariable=self.img_size_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(config_frame, text="Learning Rate:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.lr_var = tk.DoubleVar(value=0.01)
        ttk.Entry(config_frame, textvariable=self.lr_var, width=10).grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)

        # Training controls
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_training_button = ttk.Button(control_frame, text="Start Training", command=self.start_training)
        self.start_training_button.pack(side=tk.LEFT, padx=5)

        self.stop_training_button = ttk.Button(control_frame, text="Stop Training", command=self.stop_training, state=tk.DISABLED)
        self.stop_training_button.pack(side=tk.LEFT, padx=5)

        # Training progress
        progress_frame = ttk.LabelFrame(self.frame, text="Training Progress")
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        self.training_log = scrolledtext.ScrolledText(progress_frame, height=10, wrap=tk.WORD)
        self.training_log.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Initialize
        self.refresh_sessions()

    def refresh_sessions(self):
        """Refresh available sessions"""
        sessions = self.session_manager.get_session_list()
        self.session_combo['values'] = sessions
        if sessions:
            self.session_combo.set(sessions[0])

    def on_session_selected(self, event=None):
        """Handle session selection"""
        selected_session = self.session_combo.get()
        if selected_session:
            self.log_training(f"Selected session: {selected_session}")

    def validate_training_data(self):
        """Validate training data for selected session"""
        selected_session = self.session_combo.get()
        if not selected_session:
            messagebox.showwarning("Warning", "Please select a session first")
            return

        session_dir = self.session_manager.base_output_dir / selected_session
        crops_dir = session_dir / "crops"
        annotations_dir = session_dir / "annotations"

        self.validation_text.delete(1.0, tk.END)

        if not crops_dir.exists():
            self.validation_text.insert(tk.END, "❌ Crops directory not found\n")
            return

        if not annotations_dir.exists():
            self.validation_text.insert(tk.END, "❌ Annotations directory not found\n")
            return

        # Count images and annotations
        image_files = list(crops_dir.rglob("*.jpg")) + list(crops_dir.rglob("*.png"))
        annotation_files = list(annotations_dir.glob("*.txt"))

        self.validation_text.insert(tk.END, f"✅ Found {len(image_files)} image files\n")
        self.validation_text.insert(tk.END, f"✅ Found {len(annotation_files)} annotation files\n")

        # Check for matching pairs
        image_stems = {f.stem for f in image_files}
        annotation_stems = {f.stem for f in annotation_files}

        matched_pairs = len(image_stems & annotation_stems)
        self.validation_text.insert(tk.END, f"✅ Matched pairs: {matched_pairs}\n")

        if matched_pairs > 0:
            self.validation_text.insert(tk.END, f"✅ Data validation passed! Ready for training.\n")
        else:
            self.validation_text.insert(tk.END, f"❌ No matching image-annotation pairs found.\n")

    def start_training(self):
        """Start YOLO training"""
        if self.is_training:
            return

        selected_session = self.session_combo.get()
        if not selected_session:
            messagebox.showwarning("Warning", "Please select a session first")
            return

        self.is_training = True
        self.start_training_button.config(state=tk.DISABLED)
        self.stop_training_button.config(state=tk.NORMAL)

        # Start training in separate thread
        self.training_thread = threading.Thread(target=self._train_yolo, args=(selected_session,), daemon=True)
        self.training_thread.start()

    def stop_training(self):
        """Stop YOLO training"""
        self.is_training = False
        self.start_training_button.config(state=tk.NORMAL)
        self.stop_training_button.config(state=tk.DISABLED)
        self.log_training("Training stopped by user")

    def _train_yolo(self, session_id):
        """Train YOLO model (placeholder implementation)"""
        try:
            self.log_training("Starting YOLO training...")
            self.log_training(f"Session: {session_id}")
            self.log_training(f"Model: {self.model_combo.get()}")
            self.log_training(f"Epochs: {self.epochs_var.get()}")
            self.log_training(f"Batch Size: {self.batch_size_var.get()}")

            # Simulate training progress
            for epoch in range(self.epochs_var.get()):
                if not self.is_training:
                    break

                progress = (epoch + 1) / self.epochs_var.get() * 100
                self.progress_var.set(progress)

                if epoch % 10 == 0:
                    self.log_training(f"Epoch {epoch + 1}/{self.epochs_var.get()}")

                time.sleep(0.1)  # Simulate training time

            if self.is_training:
                self.log_training("Training completed successfully!")

        except Exception as e:
            self.log_training(f"Training error: {e}")
        finally:
            self.is_training = False
            self.start_training_button.config(state=tk.NORMAL)
            self.stop_training_button.config(state=tk.DISABLED)

    def log_training(self, message):
        """Log training message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.training_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.training_log.see(tk.END)

    def pack(self, **kwargs):
        """Pack the widget"""
        self.frame.pack(**kwargs)

class YOLODetectionModule:
    """YOLO detection module for the GUI"""

    def __init__(self, parent, language_manager):
        self.parent = parent
        self.lang_mgr = language_manager

        # Detection variables
        self.model_path = tk.StringVar()
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.confidence_threshold = tk.DoubleVar(value=0.25)
        self.nms_threshold = tk.DoubleVar(value=0.45)

        # Detection results
        self.detection_results = []
        self.current_result_index = 0

        # Create widgets
        self.create_widgets()

    def create_widgets(self):
        """Create YOLO detection widgets"""
        # Main frame
        self.frame = ttk.Frame(self.parent)

        # Model configuration
        model_frame = ttk.LabelFrame(self.frame, text="Model Configuration")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(model_frame, text="YOLO Model:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(model_frame, textvariable=self.model_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(model_frame, text="Browse", command=self.browse_model).grid(row=0, column=2, padx=5, pady=5)

        # Detection parameters
        params_frame = ttk.LabelFrame(self.frame, text="Detection Parameters")
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(params_frame, text="Confidence:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(params_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=self.confidence_threshold, length=200).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(params_frame, textvariable=self.confidence_threshold).grid(row=0, column=2, padx=5, pady=5)

        ttk.Label(params_frame, text="NMS Threshold:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(params_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=self.nms_threshold, length=200).grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(params_frame, textvariable=self.nms_threshold).grid(row=1, column=2, padx=5, pady=5)

        # Input/Output configuration
        io_frame = ttk.LabelFrame(self.frame, text="Input/Output")
        io_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(io_frame, text="Input Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(io_frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_input).grid(row=0, column=2, padx=5, pady=5)

        ttk.Label(io_frame, text="Output Directory:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(io_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_output).grid(row=1, column=2, padx=5, pady=5)

        # Detection controls
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="Start Detection", command=self.start_detection).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Load Results", command=self.load_results).pack(side=tk.LEFT, padx=5)

        # Results display
        results_frame = ttk.LabelFrame(self.frame, text="Detection Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create notebook for results
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Visualization tab
        viz_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(viz_frame, text="Visualization")

        # Create image preview for detection results
        self.detection_preview = ImagePreviewWidget(viz_frame, width=600, height=400)
        self.detection_preview.pack(fill=tk.BOTH, expand=True)

        # Statistics tab
        stats_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(stats_frame, text="Statistics")

        self.stats_text = scrolledtext.ScrolledText(stats_frame, wrap=tk.WORD)
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def browse_model(self):
        """Browse for YOLO model file"""
        filename = filedialog.askopenfilename(
            title="Select YOLO Model",
            filetypes=[("PyTorch Model", "*.pt"), ("All Files", "*.*")]
        )
        if filename:
            self.model_path.set(filename)

    def browse_input(self):
        """Browse for input directory"""
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)

    def browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)

    def start_detection(self):
        """Start YOLO detection"""
        if not self.model_path.get():
            messagebox.showwarning("Warning", "Please select a YOLO model file")
            return

        if not self.input_dir.get():
            messagebox.showwarning("Warning", "Please select an input directory")
            return

        if not self.output_dir.get():
            messagebox.showwarning("Warning", "Please select an output directory")
            return

        # Start detection in separate thread
        detection_thread = threading.Thread(target=self._run_detection, daemon=True)
        detection_thread.start()

    def _run_detection(self):
        """Run YOLO detection (placeholder implementation)"""
        try:
            # This would be replaced with actual YOLO detection code
            messagebox.showinfo("Info", "YOLO detection started (placeholder implementation)")

            # Simulate detection results
            input_path = Path(self.input_dir.get())
            image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))

            self.detection_results = []
            for img_file in image_files[:5]:  # Limit for demo
                # Simulate detection result
                result = {
                    'image_path': str(img_file),
                    'detections': [
                        {'class': 'seed', 'confidence': 0.85, 'bbox': [100, 100, 200, 200]},
                        {'class': 'seed', 'confidence': 0.92, 'bbox': [300, 150, 400, 250]}
                    ]
                }
                self.detection_results.append(result)

            # Update GUI
            self.update_detection_display()

        except Exception as e:
            messagebox.showerror("Error", f"Detection failed: {e}")

    def load_results(self):
        """Load existing detection results"""
        filename = filedialog.askopenfilename(
            title="Load Detection Results",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    self.detection_results = json.load(f)
                self.update_detection_display()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load results: {e}")

    def update_detection_display(self):
        """Update detection results display"""
        if not self.detection_results:
            return

        # Update image preview with detection results
        result_images = [result['image_path'] for result in self.detection_results]
        self.detection_preview.load_images(result_images)

        # Update statistics
        self.update_statistics()

    def update_statistics(self):
        """Update detection statistics"""
        if not self.detection_results:
            return

        total_images = len(self.detection_results)
        total_detections = sum(len(result['detections']) for result in self.detection_results)
        avg_confidence = 0

        if total_detections > 0:
            all_confidences = []
            for result in self.detection_results:
                for det in result['detections']:
                    all_confidences.append(det['confidence'])
            avg_confidence = sum(all_confidences) / len(all_confidences)

        stats_text = f"""Detection Statistics:

Total Images Processed: {total_images}
Total Detections: {total_detections}
Average Confidence: {avg_confidence:.3f}
Detections per Image: {total_detections / total_images if total_images > 0 else 0:.2f}

Class Distribution:
"""

        # Count classes
        class_counts = {}
        for result in self.detection_results:
            for det in result['detections']:
                class_name = det['class']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

        for class_name, count in class_counts.items():
            stats_text += f"  {class_name}: {count}\n"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_text)

    def pack(self, **kwargs):
        """Pack the widget"""
        self.frame.pack(**kwargs)

class SAMGUIAdvanced:
    """Advanced SAM GUI with enhanced features"""

    def __init__(self, root):
        self.root = root
        self.config_file = "sam_gui_config.json"

        # Initialize managers
        self.lang_mgr = LanguageManager(self.config_file)
        self.session_mgr = SessionManager("output")

        # Load configuration
        self.load_config()

        # Initialize variables
        self.init_variables()

        # Setup GUI
        self.setup_gui()

        # Initialize backend
        self.init_backend()

        # Update language
        self.update_language()

    def load_config(self):
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            self.config = {}

    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")

    def init_variables(self):
        """Initialize GUI variables"""
        # File paths
        self.input_dir = tk.StringVar(value=self.config.get('input_dir', ''))
        self.output_dir = tk.StringVar(value=self.config.get('output_dir', 'output'))
        self.model_path = tk.StringVar(value=self.config.get('model_path', 'sam_vit_h_4b8939.pth'))

        # Processing settings
        self.device = tk.StringVar(value=self.config.get('device', 'cpu'))
        self.preview_mode = tk.BooleanVar(value=self.config.get('preview_mode', True))
        self.max_preview_images = tk.IntVar(value=self.config.get('max_preview_images', 10))
        self.save_debug_images = tk.BooleanVar(value=self.config.get('save_debug_images', True))
        self.create_yolo_annotations = tk.BooleanVar(value=self.config.get('create_yolo_annotations', True))

        # Processing state
        self.is_processing = False
        self.processing_thread = None
        self.current_session_id = tk.StringVar()

        # Statistics
        self.processed_count = tk.IntVar()
        self.total_count = tk.IntVar()
        self.seeds_detected = tk.IntVar()
        self.processing_speed = tk.DoubleVar()
        self.eta_text = tk.StringVar(value="--:--")

    def setup_gui(self):
        """Setup the GUI"""
        # Configure root window
        gui_settings = self.config.get('gui_settings', {})
        self.root.title(self.lang_mgr.get_text('app_title'))
        self.root.geometry(f"{gui_settings.get('window_width', 1400)}x{gui_settings.get('window_height', 900)}")

        # Create menu bar
        self.create_menu_bar()

        # Create main interface
        self.create_main_interface()

        # Create status bar
        self.create_status_bar()

    def create_menu_bar(self):
        """Create menu bar with language toggle"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Load Config", command=self.load_config_file)
        file_menu.add_command(label="Save Config", command=self.save_config_file)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)

        # Language menu
        language_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Language / 语言", menu=language_menu)
        language_menu.add_command(label="English", command=lambda: self.change_language('en'))
        language_menu.add_command(label="中文", command=lambda: self.change_language('zh'))

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)

    def create_main_interface(self):
        """Create main interface with tabs"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # File Settings tab
        self.file_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.file_frame, text=self.lang_mgr.get_text('file_settings'))
        self.create_file_settings()

        # Parameters tab
        self.params_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.params_frame, text=self.lang_mgr.get_text('parameters'))
        self.create_parameters_tab()

        # Processing Control tab
        self.control_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.control_frame, text=self.lang_mgr.get_text('processing_control'))
        self.create_processing_control()

        # Results Preview tab
        self.preview_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.preview_frame, text=self.lang_mgr.get_text('preview'))
        self.create_preview_tab()

        # YOLO Training tab
        self.training_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.training_frame, text=self.lang_mgr.get_text('yolo_training'))
        self.yolo_training = YOLOTrainingModule(self.training_frame, self.session_mgr, self.lang_mgr)
        self.yolo_training.pack(fill=tk.BOTH, expand=True)

        # YOLO Detection tab
        self.detection_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.detection_frame, text=self.lang_mgr.get_text('yolo_detection'))
        self.yolo_detection = YOLODetectionModule(self.detection_frame, self.lang_mgr)
        self.yolo_detection.pack(fill=tk.BOTH, expand=True)

    def create_file_settings(self):
        """Create file settings tab"""
        # Session management
        session_frame = ttk.LabelFrame(self.file_frame, text="Session Management")
        session_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(session_frame, text=self.lang_mgr.get_text('session_id')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(session_frame, textvariable=self.current_session_id).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(session_frame, text="New Session", command=self.create_new_session).grid(row=0, column=2, padx=5, pady=5)

        # File paths
        paths_frame = ttk.LabelFrame(self.file_frame, text="File Paths")
        paths_frame.pack(fill=tk.X, padx=5, pady=5)

        # Input directory
        ttk.Label(paths_frame, text=self.lang_mgr.get_text('input_dir')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.lang_mgr.get_text('browse'), command=self.browse_input_dir).grid(row=0, column=2, padx=5, pady=5)

        # Output directory
        ttk.Label(paths_frame, text=self.lang_mgr.get_text('output_dir')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.lang_mgr.get_text('browse'), command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=5)

        # Model path
        ttk.Label(paths_frame, text=self.lang_mgr.get_text('model_path')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.model_path, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.lang_mgr.get_text('browse'), command=self.browse_model_path).grid(row=2, column=2, padx=5, pady=5)

        # Processing options
        options_frame = ttk.LabelFrame(self.file_frame, text="Processing Options")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(options_frame, text=self.lang_mgr.get_text('device')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        device_combo = ttk.Combobox(options_frame, textvariable=self.device, values=['cpu', 'cuda'], state="readonly")
        device_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Checkbutton(options_frame, text=self.lang_mgr.get_text('preview_mode'), variable=self.preview_mode).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        ttk.Label(options_frame, text=self.lang_mgr.get_text('max_preview')).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Spinbox(options_frame, from_=1, to=100, textvariable=self.max_preview_images, width=10).grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        ttk.Checkbutton(options_frame, text=self.lang_mgr.get_text('save_debug'), variable=self.save_debug_images).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(options_frame, text=self.lang_mgr.get_text('create_yolo'), variable=self.create_yolo_annotations).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Create initial session
        self.create_new_session()

    def create_parameters_tab(self):
        """Create parameters configuration tab"""
        # SAM Parameters
        sam_frame = ttk.LabelFrame(self.params_frame, text="SAM Parameters")
        sam_frame.pack(fill=tk.X, padx=5, pady=5)

        # Points per side
        ttk.Label(sam_frame, text="Points per side:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.points_per_side = tk.IntVar(value=self.config.get('points_per_side', 32))
        ttk.Spinbox(sam_frame, from_=8, to=128, textvariable=self.points_per_side, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Prediction IoU threshold
        ttk.Label(sam_frame, text="Pred IoU threshold:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.pred_iou_thresh = tk.DoubleVar(value=self.config.get('pred_iou_thresh', 0.88))
        ttk.Scale(sam_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.pred_iou_thresh, length=150).grid(row=0, column=3, padx=5, pady=5)

        # Stability score threshold
        ttk.Label(sam_frame, text="Stability score:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.stability_score_thresh = tk.DoubleVar(value=self.config.get('stability_score_thresh', 0.95))
        ttk.Scale(sam_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.stability_score_thresh, length=150).grid(row=1, column=1, padx=5, pady=5)

        # Seed filtering parameters
        filter_frame = ttk.LabelFrame(self.params_frame, text="Seed Filtering")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # Area filters
        ttk.Label(filter_frame, text="Min area:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.min_seed_area = tk.IntVar(value=self.config.get('min_seed_area', 100))
        ttk.Spinbox(filter_frame, from_=10, to=10000, textvariable=self.min_seed_area, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(filter_frame, text="Max area:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.max_seed_area = tk.IntVar(value=self.config.get('max_seed_area', 50000))
        ttk.Spinbox(filter_frame, from_=100, to=100000, textvariable=self.max_seed_area, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # Aspect ratio filters
        ttk.Label(filter_frame, text="Min aspect ratio:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.min_aspect_ratio = tk.DoubleVar(value=self.config.get('min_aspect_ratio', 0.3))
        ttk.Scale(filter_frame, from_=0.1, to=1.0, orient=tk.HORIZONTAL, variable=self.min_aspect_ratio, length=100).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(filter_frame, text="Max aspect ratio:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.max_aspect_ratio = tk.DoubleVar(value=self.config.get('max_aspect_ratio', 3.0))
        ttk.Scale(filter_frame, from_=1.0, to=5.0, orient=tk.HORIZONTAL, variable=self.max_aspect_ratio, length=100).grid(row=1, column=3, padx=5, pady=5)

        # Solidity filter
        ttk.Label(filter_frame, text="Min solidity:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.min_solidity = tk.DoubleVar(value=self.config.get('min_solidity', 0.8))
        ttk.Scale(filter_frame, from_=0.5, to=1.0, orient=tk.HORIZONTAL, variable=self.min_solidity, length=150).grid(row=2, column=1, padx=5, pady=5)

        # Preset configurations
        preset_frame = ttk.LabelFrame(self.params_frame, text="Preset Configurations")
        preset_frame.pack(fill=tk.X, padx=5, pady=5)

        presets = ["High Precision", "Balanced", "Fast", "Large Seeds", "Small Seeds"]
        for i, preset in enumerate(presets):
            ttk.Button(preset_frame, text=preset, command=lambda p=preset: self.apply_preset(p)).grid(row=0, column=i, padx=5, pady=5)

    def create_processing_control(self):
        """Create processing control tab"""
        # Control buttons
        control_frame = ttk.Frame(self.control_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_button = ttk.Button(control_frame, text=self.lang_mgr.get_text('start_processing'), command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.pause_button = ttk.Button(control_frame, text=self.lang_mgr.get_text('pause'), command=self.pause_processing, state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(control_frame, text=self.lang_mgr.get_text('stop'), command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # Progress display
        progress_frame = ttk.LabelFrame(self.control_frame, text="Progress")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        # Current image
        ttk.Label(progress_frame, text=self.lang_mgr.get_text('current_image')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.current_image_label = ttk.Label(progress_frame, text="--")
        self.current_image_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Progress bar
        ttk.Label(progress_frame, text=self.lang_mgr.get_text('processed_total')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=300)
        self.progress_bar.grid(row=1, column=1, padx=5, pady=5)

        # Statistics
        stats_frame = ttk.LabelFrame(self.control_frame, text="Statistics")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(stats_frame, text=self.lang_mgr.get_text('seeds_detected')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.seeds_detected).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(stats_frame, text=self.lang_mgr.get_text('processing_speed')).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.processing_speed).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(stats_frame, text=self.lang_mgr.get_text('eta')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(stats_frame, textvariable=self.eta_text).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Processing log
        log_frame = ttk.LabelFrame(self.control_frame, text=self.lang_mgr.get_text('processing_log'))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Log controls
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_control_frame, text=self.lang_mgr.get_text('clear_log'), command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text=self.lang_mgr.get_text('save_log'), command=self.save_log).pack(side=tk.LEFT, padx=5)

    def create_preview_tab(self):
        """Create enhanced preview tab"""
        # Control frame
        control_frame = ttk.Frame(self.preview_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text=self.lang_mgr.get_text('refresh_preview'), command=self.refresh_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text=self.lang_mgr.get_text('open_output'), command=self.open_output_directory).pack(side=tk.LEFT, padx=5)

        # Create image preview widget
        self.preview_widget = ImagePreviewWidget(self.preview_frame, width=800, height=600)
        self.preview_widget.pack(fill=tk.BOTH, expand=True)

    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_text = tk.StringVar(value="Ready")
        ttk.Label(self.status_bar, textvariable=self.status_text).pack(side=tk.LEFT, padx=5)

        # Language indicator
        ttk.Label(self.status_bar, text=f"Language: {self.lang_mgr.current_language.upper()}").pack(side=tk.RIGHT, padx=5)

    def init_backend(self):
        """Initialize SAM backend"""
        if not BACKEND_AVAILABLE:
            print("Warning: SAM backend not available. Some features may not work.")
            return

        try:
            # Initialize SAM segmenter (will be created when processing starts)
            self.sam_segmenter = None
            print("Backend initialized successfully")
        except Exception as e:
            print(f"Backend initialization error: {e}")

    def change_language(self, language):
        """Change GUI language"""
        self.lang_mgr.set_language(language)
        self.update_language()
        # Only log if log_text widget exists
        if hasattr(self, 'log_text'):
            self.log_message(f"Language changed to: {language}")
        else:
            print(f"Language changed to: {language}")

    def update_language(self):
        """Update all GUI text to current language"""
        # Update window title
        self.root.title(self.lang_mgr.get_text('app_title'))

        # Update tab titles
        self.notebook.tab(0, text=self.lang_mgr.get_text('file_settings'))
        self.notebook.tab(1, text=self.lang_mgr.get_text('parameters'))
        self.notebook.tab(2, text=self.lang_mgr.get_text('processing_control'))
        self.notebook.tab(3, text=self.lang_mgr.get_text('preview'))
        self.notebook.tab(4, text=self.lang_mgr.get_text('yolo_training'))
        self.notebook.tab(5, text=self.lang_mgr.get_text('yolo_detection'))

        # Update status bar
        for widget in self.status_bar.winfo_children():
            if isinstance(widget, ttk.Label) and "Language:" in str(widget.cget('text')):
                widget.config(text=f"Language: {self.lang_mgr.current_language.upper()}")

    def create_new_session(self):
        """Create a new processing session"""
        session_id, session_dir = self.session_mgr.create_new_session()
        self.current_session_id.set(session_id)
        # Only log if log_text widget exists
        if hasattr(self, 'log_text'):
            self.log_message(f"Created new session: {session_id}")
        else:
            print(f"Created new session: {session_id}")
        return session_id, session_dir

    def browse_input_dir(self):
        """Browse for input directory"""
        directory = filedialog.askdirectory(title=self.lang_mgr.get_text('input_dir'))
        if directory:
            self.input_dir.set(directory)

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title=self.lang_mgr.get_text('output_dir'))
        if directory:
            self.output_dir.set(directory)
            # Update session manager base directory
            self.session_mgr = SessionManager(directory)

    def browse_model_path(self):
        """Browse for SAM model file"""
        filename = filedialog.askopenfilename(
            title=self.lang_mgr.get_text('model_path'),
            filetypes=[("PyTorch Model", "*.pth"), ("All Files", "*.*")]
        )
        if filename:
            self.model_path.set(filename)

    def apply_preset(self, preset_name):
        """Apply preset configuration"""
        presets = self.config.get('presets', {})
        if preset_name in presets:
            preset = presets[preset_name]

            # Apply SAM parameters
            self.points_per_side.set(preset.get('points_per_side', 32))
            self.pred_iou_thresh.set(preset.get('pred_iou_thresh', 0.88))
            self.stability_score_thresh.set(preset.get('stability_score_thresh', 0.95))

            # Apply filtering parameters
            self.min_seed_area.set(preset.get('min_seed_area', 100))
            self.max_seed_area.set(preset.get('max_seed_area', 50000))
            self.min_aspect_ratio.set(preset.get('min_aspect_ratio', 0.3))
            self.max_aspect_ratio.set(preset.get('max_aspect_ratio', 3.0))
            self.min_solidity.set(preset.get('min_solidity', 0.8))

            # Only log if log_text widget exists
            if hasattr(self, 'log_text'):
                self.log_message(f"Applied preset: {preset_name}")
            else:
                print(f"Applied preset: {preset_name}")

    def start_processing(self):
        """Start SAM processing"""
        if self.is_processing:
            return

        # Validate inputs
        if not self.input_dir.get():
            messagebox.showwarning("Warning", "Please select an input directory")
            return

        if not Path(self.input_dir.get()).exists():
            messagebox.showerror("Error", "Input directory does not exist")
            return

        if not Path(self.model_path.get()).exists():
            messagebox.showwarning("Warning", "SAM model file not found. Please check the path.")
            return

        # Create new session if needed
        if not self.session_mgr.current_session_id:
            self.create_new_session()

        # Start processing in separate thread
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL)

        self.processing_thread = threading.Thread(target=self._process_images, daemon=True)
        self.processing_thread.start()

        if hasattr(self, 'log_text'):
            self.log_message("Processing started")
        else:
            print("Processing started")

    def pause_processing(self):
        """Pause processing"""
        # Implementation would depend on backend support
        if hasattr(self, 'log_text'):
            self.log_message("Pause functionality not implemented yet")
        else:
            print("Pause functionality not implemented yet")

    def stop_processing(self):
        """Stop processing"""
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.DISABLED)
        if hasattr(self, 'log_text'):
            self.log_message("Processing stopped")
        else:
            print("Processing stopped")

    def _process_images(self):
        """Process images using SAM (placeholder implementation)"""
        try:
            input_path = Path(self.input_dir.get())
            image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))

            if not image_files:
                self.log_message("No image files found in input directory")
                return

            self.total_count.set(len(image_files))

            # Limit images in preview mode
            if self.preview_mode.get():
                image_files = image_files[:self.max_preview_images.get()]
                self.log_message(f"Preview mode: processing {len(image_files)} images")

            # Process each image
            for i, image_file in enumerate(image_files):
                if not self.is_processing:
                    break

                self.current_image_label.config(text=image_file.name)
                self.processed_count.set(i + 1)

                # Update progress
                progress = (i + 1) / len(image_files) * 100
                self.progress_var.set(progress)

                # Simulate processing
                time.sleep(0.5)  # Replace with actual SAM processing

                # Update statistics
                detected_seeds = np.random.randint(1, 10)  # Placeholder
                self.seeds_detected.set(self.seeds_detected.get() + detected_seeds)

                self.log_message(f"Processed {image_file.name}: {detected_seeds} seeds detected")

            if self.is_processing:
                self.log_message("Processing completed successfully")
                self.refresh_preview()

        except Exception as e:
            self.log_message(f"Processing error: {e}")
        finally:
            self.is_processing = False
            self.start_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)

    def refresh_preview(self):
        """Refresh preview images"""
        if not self.session_mgr.current_session_dir:
            return

        debug_dir = self.session_mgr.current_session_dir / "debug"
        if debug_dir.exists():
            debug_images = list(debug_dir.glob("*.jpg")) + list(debug_dir.glob("*.png"))
            if debug_images:
                if hasattr(self, 'preview_widget'):
                    self.preview_widget.load_images(debug_images)
                if hasattr(self, 'log_text'):
                    self.log_message(f"Loaded {len(debug_images)} preview images")
                else:
                    print(f"Loaded {len(debug_images)} preview images")
            else:
                if hasattr(self, 'log_text'):
                    self.log_message("No debug images found")
                else:
                    print("No debug images found")
        else:
            if hasattr(self, 'log_text'):
                self.log_message("Debug directory not found")
            else:
                print("Debug directory not found")

    def open_output_directory(self):
        """Open output directory in file explorer"""
        if self.session_mgr.current_session_dir and self.session_mgr.current_session_dir.exists():
            if sys.platform == "win32":
                os.startfile(self.session_mgr.current_session_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", self.session_mgr.current_session_dir])
            else:
                subprocess.run(["xdg-open", self.session_mgr.current_session_dir])
        else:
            messagebox.showwarning("Warning", "Output directory not found")

    def log_message(self, message):
        """Log message to processing log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Only update GUI if widgets exist
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        else:
            print(log_entry.strip())  # Fallback to console

        if hasattr(self, 'status_text') and self.status_text:
            self.status_text.set(message)
        else:
            print(f"Status: {message}")  # Fallback to console

    def clear_log(self):
        """Clear processing log"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """Save processing log to file"""
        filename = filedialog.asksaveasfilename(
            title="Save Log",
            defaultextension=".txt",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"Log saved to: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save log: {e}")

    def load_config_file(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.log_message(f"Configuration loaded from: {filename}")
                # Update GUI with loaded config
                self.update_gui_from_config()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def save_config_file(self):
        """Save configuration to file"""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")]
        )
        if filename:
            try:
                # Update config with current GUI values
                self.update_config_from_gui()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                self.log_message(f"Configuration saved to: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def update_gui_from_config(self):
        """Update GUI values from configuration"""
        # Update file paths
        self.input_dir.set(self.config.get('input_dir', ''))
        self.output_dir.set(self.config.get('output_dir', 'output'))
        self.model_path.set(self.config.get('model_path', 'sam_vit_h_4b8939.pth'))

        # Update processing settings
        self.device.set(self.config.get('device', 'cpu'))
        self.preview_mode.set(self.config.get('preview_mode', True))
        self.max_preview_images.set(self.config.get('max_preview_images', 10))
        self.save_debug_images.set(self.config.get('save_debug_images', True))
        self.create_yolo_annotations.set(self.config.get('create_yolo_annotations', True))

        # Update SAM parameters
        if hasattr(self, 'points_per_side'):
            self.points_per_side.set(self.config.get('points_per_side', 32))
            self.pred_iou_thresh.set(self.config.get('pred_iou_thresh', 0.88))
            self.stability_score_thresh.set(self.config.get('stability_score_thresh', 0.95))

            # Update filtering parameters
            self.min_seed_area.set(self.config.get('min_seed_area', 100))
            self.max_seed_area.set(self.config.get('max_seed_area', 50000))
            self.min_aspect_ratio.set(self.config.get('min_aspect_ratio', 0.3))
            self.max_aspect_ratio.set(self.config.get('max_aspect_ratio', 3.0))
            self.min_solidity.set(self.config.get('min_solidity', 0.8))

    def update_config_from_gui(self):
        """Update configuration from GUI values"""
        # Update file paths
        self.config['input_dir'] = self.input_dir.get()
        self.config['output_dir'] = self.output_dir.get()
        self.config['model_path'] = self.model_path.get()

        # Update processing settings
        self.config['device'] = self.device.get()
        self.config['preview_mode'] = self.preview_mode.get()
        self.config['max_preview_images'] = self.max_preview_images.get()
        self.config['save_debug_images'] = self.save_debug_images.get()
        self.config['create_yolo_annotations'] = self.create_yolo_annotations.get()

        # Update SAM parameters
        if hasattr(self, 'points_per_side'):
            self.config['points_per_side'] = self.points_per_side.get()
            self.config['pred_iou_thresh'] = self.pred_iou_thresh.get()
            self.config['stability_score_thresh'] = self.stability_score_thresh.get()

            # Update filtering parameters
            self.config['min_seed_area'] = self.min_seed_area.get()
            self.config['max_seed_area'] = self.max_seed_area.get()
            self.config['min_aspect_ratio'] = self.min_aspect_ratio.get()
            self.config['max_aspect_ratio'] = self.max_aspect_ratio.get()
            self.config['min_solidity'] = self.min_solidity.get()

    def show_about(self):
        """Show about dialog"""
        about_text = f"""SAM Seed Segmentation Tool - Advanced GUI

Version: 2.0
Based on Meta's Segment Anything Model (SAM)

Features:
• Language toggle system (English/Chinese)
• Session-based output organization
• Enhanced results preview with navigation
• Integrated YOLO training module
• YOLO detection and visualization
• High-precision seed segmentation

Current Language: {self.lang_mgr.current_language.upper()}
Current Session: {self.current_session_id.get()}

© 2024 SAM Seed Segmentation Project"""

        messagebox.showinfo("About", about_text)

def main():
    """Main function to run the advanced GUI"""
    root = tk.Tk()
    app = SAMGUIAdvanced(root)

    # Handle window closing
    def on_closing():
        if app.is_processing:
            if messagebox.askokcancel("Quit", "Processing is running. Do you want to quit?"):
                app.stop_processing()
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")

if __name__ == "__main__":
    main()
