# SAM Seed Segmentation Tool - PowerShell Launcher
# PowerShell script with better Unicode support

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "SAM Seed Segmentation Tool - GUI Launcher"

# Clear screen and show header
Clear-Host
Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "    SAM Seed Segmentation Tool" -ForegroundColor White
Write-Host "    GUI Launcher v1.0" -ForegroundColor White  
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Function to write colored output
function Write-Status {
    param(
        [string]$Message,
        [string]$Status = "INFO",
        [string]$Color = "White"
    )
    
    $statusColor = switch ($Status) {
        "OK" { "Green" }
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "INFO" { "Cyan" }
        default { "White" }
    }
    
    Write-Host "[$Status] " -ForegroundColor $statusColor -NoNewline
    Write-Host $Message -ForegroundColor $Color
}

# Check Python installation
Write-Status "Checking Python installation..." "INFO"

try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Status "Python found: $pythonVersion" "OK"
    } else {
        throw "Python not found"
    }
} catch {
    Write-Status "Python is not installed or not in PATH" "ERROR"
    Write-Host ""
    Write-Host "Please install Python 3.8 or higher:" -ForegroundColor Yellow
    Write-Host "- Download from: https://www.python.org/downloads/" -ForegroundColor Yellow
    Write-Host "- Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check required files
Write-Status "Checking required files..." "INFO"

$requiredFiles = @(
    "launch_sam_gui.py",
    "sam_gui.py", 
    "sam_seed_segmenter.py"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Status "Missing required files:" "ERROR"
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
} else {
    Write-Status "All required Python files found" "OK"
}

# Check SAM model file
Write-Status "Checking SAM model file..." "INFO"
if (Test-Path "sam_vit_h_4b8939.pth") {
    $fileSize = (Get-Item "sam_vit_h_4b8939.pth").Length / 1GB
    Write-Status "SAM model file found (${fileSize:F1} GB)" "OK"
} else {
    Write-Status "SAM model file not found: sam_vit_h_4b8939.pth" "WARNING"
    Write-Host ""
    Write-Host "You can download it from:" -ForegroundColor Yellow
    Write-Host "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth" -ForegroundColor Yellow
    Write-Host ""
    $continue = Read-Host "Continue anyway? (Y/N)"
    if ($continue -ne "Y" -and $continue -ne "y") {
        exit 1
    }
}

# Launch the GUI
Write-Status "Launching SAM GUI..." "INFO"
Write-Host ""
Write-Host "Starting Python GUI application..." -ForegroundColor Green
Write-Host "(This may take a moment to load)" -ForegroundColor Yellow
Write-Host ""

try {
    python launch_sam_gui.py
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Status "GUI closed successfully" "OK"
        Write-Host ""
        Write-Host "Thank you for using SAM Seed Segmentation Tool!" -ForegroundColor Green
    } else {
        throw "GUI failed to start"
    }
} catch {
    Write-Host ""
    Write-Status "GUI failed to start" "ERROR"
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Install required packages:" -ForegroundColor Yellow
    Write-Host "   pip install segment-anything opencv-python numpy matplotlib pillow" -ForegroundColor White
    Write-Host ""
    Write-Host "2. Check Python environment:" -ForegroundColor Yellow
    Write-Host "   python -c `"import tkinter; print('tkinter OK')`"" -ForegroundColor White
    Write-Host ""
    Write-Host "3. Check error messages above for specific issues" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host ""
Read-Host "Press Enter to exit"
