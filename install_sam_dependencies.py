#!/usr/bin/env python3
"""
安装SAM依赖的脚本
Script to install SAM dependencies
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"🔧 {description}")
    print(f"执行: {command}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ 成功!")
        print("✅ Success!")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        print(f"❌ Failed: {e}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print("❌ Python 3.8 or higher required")
        return False
    
    print("✅ Python版本符合要求")
    print("✅ Python version meets requirements")
    return True

def install_pytorch():
    """安装PyTorch"""
    print("\n📦 安装PyTorch...")
    print("📦 Installing PyTorch...")
    
    # 检查是否已安装
    try:
        import torch
        print(f"✅ PyTorch已安装: {torch.__version__}")
        print(f"✅ PyTorch already installed: {torch.__version__}")
        return True
    except ImportError:
        pass
    
    # 安装PyTorch (CPU版本，兼容性更好)
    pytorch_command = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    
    return run_command(pytorch_command, "安装PyTorch (CPU版本)")

def install_segment_anything():
    """安装Segment Anything"""
    print("\n📦 安装Segment Anything...")
    print("📦 Installing Segment Anything...")
    
    # 检查是否已安装
    try:
        import segment_anything
        print("✅ Segment Anything已安装")
        print("✅ Segment Anything already installed")
        return True
    except ImportError:
        pass
    
    # 安装segment-anything
    sam_command = "pip install segment-anything"
    
    return run_command(sam_command, "安装Segment Anything")

def install_other_dependencies():
    """安装其他依赖"""
    print("\n📦 安装其他依赖...")
    print("📦 Installing other dependencies...")
    
    dependencies = [
        "opencv-python>=4.8.0",
        "numpy>=1.24.0", 
        "matplotlib>=3.5.0",
        "pillow>=9.0.0",
        "scikit-image>=0.20.0"
    ]
    
    for dep in dependencies:
        command = f"pip install {dep}"
        if not run_command(command, f"安装 {dep}"):
            return False
    
    return True

def check_model_file():
    """检查模型文件"""
    print("\n📁 检查SAM模型文件...")
    print("📁 Checking SAM model file...")
    
    model_path = "sam_vit_h_4b8939.pth"
    
    if Path(model_path).exists():
        size_gb = Path(model_path).stat().st_size / (1024**3)
        print(f"✅ 模型文件存在: {model_path} ({size_gb:.1f} GB)")
        print(f"✅ Model file exists: {model_path} ({size_gb:.1f} GB)")
        return True
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        print(f"❌ Model file not found: {model_path}")
        print("\n📥 请下载SAM模型文件:")
        print("📥 Please download SAM model file:")
        print("1. 访问: https://github.com/facebookresearch/segment-anything#model-checkpoints")
        print("2. 下载 sam_vit_h_4b8939.pth (约2.4GB)")
        print("3. 将文件放在当前目录中")
        print("1. Visit: https://github.com/facebookresearch/segment-anything#model-checkpoints")
        print("2. Download sam_vit_h_4b8939.pth (~2.4GB)")
        print("3. Place the file in current directory")
        return False

def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    print("🧪 Testing installation...")
    
    try:
        # 测试PyTorch
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        # 测试Segment Anything
        from segment_anything import sam_model_registry
        print("✅ Segment Anything: 可以导入")
        print("✅ Segment Anything: Can import")
        
        # 测试其他依赖
        import cv2
        import numpy as np
        import matplotlib
        from PIL import Image
        
        print("✅ 所有依赖测试通过!")
        print("✅ All dependencies test passed!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SAM种子分割工具 - 依赖安装")
    print("🚀 SAM Seed Segmentation Tool - Dependency Installation")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    steps = [
        ("安装PyTorch", install_pytorch),
        ("安装Segment Anything", install_segment_anything), 
        ("安装其他依赖", install_other_dependencies),
        ("检查模型文件", check_model_file),
        ("测试安装", test_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"\n❌ {step_name}失败")
            print(f"❌ {step_name} failed")
            return 1
    
    print("\n🎉 安装完成!")
    print("🎉 Installation complete!")
    print("\n📝 下一步:")
    print("📝 Next steps:")
    print("1. 如果模型文件缺失，请下载 sam_vit_h_4b8939.pth")
    print("2. 运行测试: python test_sam_setup.py")
    print("3. 运行分割: python sam_seed_segmenter.py \"CVH-seed-pic\" \"./sam_output\" --preview")
    print("1. If model file is missing, download sam_vit_h_4b8939.pth")
    print("2. Run test: python test_sam_setup.py")
    print("3. Run segmentation: python sam_seed_segmenter.py \"CVH-seed-pic\" \"./sam_output\" --preview")
    
    return 0

if __name__ == "__main__":
    exit(main())
