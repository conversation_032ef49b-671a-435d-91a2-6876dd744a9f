#!/usr/bin/env python3
"""
Test script to run the seed classification tool on a small sample.
This script will process 3-5 images from the CVH dataset.
"""

import os
import sys
import traceback
from pathlib import Path

# Add current directory to path to import our modules
sys.path.insert(0, '.')

def main():
    """Run the seed classification test."""
    print("=" * 60)
    print("SEED CLASSIFICATION TOOL - TEST RUN")
    print("=" * 60)
    
    try:
        # Import our modules
        print("1. Importing modules...")
        from seed_classifier import SeedProcessor, SegmentationConfig, ProcessingConfig
        print("   ✓ Modules imported successfully")
        
        # Check input directory
        print("\n2. Checking input directory...")
        input_dir = "CVH-seed-pic/CVH-seed-pic"
        if not os.path.exists(input_dir):
            print(f"   ✗ Input directory not found: {input_dir}")
            return False
        
        # Count available images
        input_path = Path(input_dir)
        image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.JPG"))
        print(f"   ✓ Found {len(image_files)} image files")
        
        if len(image_files) == 0:
            print("   ✗ No image files found")
            return False
        
        # Show first few files
        print("   Sample files:")
        for i, img_file in enumerate(image_files[:5]):
            print(f"     - {img_file.name}")
        
        # Set up configuration
        print("\n3. Setting up configuration...")
        seg_config = SegmentationConfig(
            # Use more permissive settings for testing
            min_contour_area=300,
            max_contour_area=50000,
            min_seed_width=15,
            min_seed_height=15,
            threshold_method="adaptive"
        )
        
        proc_config = ProcessingConfig(
            input_directory=input_dir,
            output_directory="./output",
            preview_mode=True,  # Process only a few images
            max_preview_images=3,  # Test with 3 images
            save_debug_images=True,
            create_yolo_annotations=True
        )
        
        print("   ✓ Configuration created")
        print(f"     - Processing mode: {'Preview' if proc_config.preview_mode else 'Full'}")
        print(f"     - Max images: {proc_config.max_preview_images}")
        print(f"     - Min contour area: {seg_config.min_contour_area}")
        
        # Create processor
        print("\n4. Creating processor...")
        processor = SeedProcessor(seg_config, proc_config)
        print("   ✓ Processor created successfully")
        
        # Test file discovery
        print("\n5. Testing file discovery...")
        discovered_files = processor.get_image_files()
        print(f"   ✓ Discovered {len(discovered_files)} files")
        
        # Test species extraction
        print("\n6. Testing species extraction...")
        test_files = discovered_files[:3]
        for file_path in test_files:
            filename = os.path.basename(file_path)
            species_id = processor.extract_species_from_filename(filename)
            print(f"     {filename} → Species: {species_id}")
        
        # Run processing
        print("\n7. Running seed processing...")
        print("   Processing images (this may take a moment)...")
        
        results = processor.process_batch()
        
        if results['success']:
            summary = results['summary']
            print("   ✓ Processing completed successfully!")
            print(f"     - Images processed: {summary['successful_images']}/{summary['total_images']}")
            print(f"     - Total seeds found: {summary['total_seeds_extracted']}")
            print(f"     - Average seeds per image: {summary['average_seeds_per_image']:.1f}")
            
            # Show species summary
            if summary['species_summary']:
                print("     - Species found:")
                for species_id, data in summary['species_summary'].items():
                    print(f"       * Species {species_id}: {data['total_seeds']} seeds from {data['images_processed']} images")
            
            # Check output structure
            print("\n8. Checking output structure...")
            output_path = Path("./output")
            
            # Check directories
            expected_dirs = ["crops", "annotations"]
            if proc_config.save_debug_images:
                expected_dirs.append("debug")
            
            for dir_name in expected_dirs:
                dir_path = output_path / dir_name
                if dir_path.exists():
                    files_count = len(list(dir_path.rglob("*.*")))
                    print(f"     ✓ {dir_name}/ directory exists with {files_count} files")
                else:
                    print(f"     ⚠ {dir_name}/ directory not found")
            
            # Check for key files
            key_files = ["class_mapping.json", "processing_summary.json"]
            for file_name in key_files:
                file_path = output_path / file_name
                if file_path.exists():
                    print(f"     ✓ {file_name} created")
                else:
                    print(f"     ⚠ {file_name} not found")
            
            # Show some example outputs
            print("\n9. Sample outputs:")
            
            # Show crop files
            crops_dir = output_path / "crops"
            if crops_dir.exists():
                crop_files = list(crops_dir.rglob("*.jpg"))
                print(f"     Crop files ({len(crop_files)} total):")
                for crop_file in crop_files[:5]:  # Show first 5
                    rel_path = crop_file.relative_to(output_path)
                    print(f"       - {rel_path}")
            
            # Show annotation files
            annotations_dir = output_path / "annotations"
            if annotations_dir.exists():
                annotation_files = list(annotations_dir.glob("*.txt"))
                print(f"     Annotation files ({len(annotation_files)} total):")
                for ann_file in annotation_files[:3]:  # Show first 3
                    print(f"       - {ann_file.name}")
                    # Show content of first annotation file
                    if ann_file.exists():
                        with open(ann_file, 'r') as f:
                            content = f.read().strip()
                            lines = content.split('\n')
                            print(f"         Content: {len(lines)} detections")
                            if lines and lines[0]:
                                print(f"         Sample: {lines[0]}")
            
            print("\n" + "=" * 60)
            print("✅ TEST COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print(f"Results saved to: {output_path.absolute()}")
            print("\nNext steps:")
            print("1. Check the output/crops/ directory for individual seed images")
            print("2. Check the output/annotations/ directory for YOLO format files")
            print("3. Review the processing_summary.json for detailed statistics")
            if proc_config.save_debug_images:
                print("4. Check the output/debug/ directory for visualization images")
            
            return True
            
        else:
            print(f"   ✗ Processing failed: {results.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"\n✗ Error during testing: {str(e)}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n❌ Test failed. Please check the errors above.")
    
    input("\nPress Enter to exit...")  # Keep window open on Windows
