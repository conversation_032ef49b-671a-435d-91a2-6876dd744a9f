#!/usr/bin/env python3
"""
Direct processing script that will definitely work.
This manually processes images and creates real crop files.
"""

def main():
    import cv2
    import numpy as np
    from pathlib import Path
    import json
    import re
    import os
    
    print("🌱 DIRECT SEED PROCESSING")
    print("="*50)
    
    # Create output directories
    output_dir = Path("output")
    if output_dir.exists():
        import shutil
        shutil.rmtree(output_dir)
    
    dirs = [
        output_dir,
        output_dir / "crops",
        output_dir / "annotations", 
        output_dir / "debug"
    ]
    
    for d in dirs:
        d.mkdir(parents=True, exist_ok=True)
    
    print("✓ Created output directories")
    
    # Find input images
    input_dir = Path("CVH-seed-pic")
    if not input_dir.exists():
        print("❌ Input directory not found")
        return

    jpg_files = list(input_dir.glob("*.jpg"))[:3]  # Process first 3
    print(f"✓ Found {len(jpg_files)} images to process")
    
    species_class_map = {}
    results = []
    
    for i, img_path in enumerate(jpg_files):
        print(f"\n📸 Processing {img_path.name}...")
        
        try:
            # Load image
            image = cv2.imread(str(img_path))
            if image is None:
                print("  ❌ Failed to load")
                continue
            
            print(f"  ✓ Loaded: {image.shape}")
            
            # Extract species ID
            match = re.search(r'S(\d+)-', img_path.name)
            species_id = match.group(1) if match else f"unknown_{i}"
            
            if species_id not in species_class_map:
                species_class_map[species_id] = len(species_class_map)
            class_id = species_class_map[species_id]
            
            print(f"  ✓ Species: {species_id}, Class: {class_id}")
            
            # Simple segmentation
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 1.0)
            
            # Adaptive threshold
            binary = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel, iterations=1)
            
            # Find contours
            contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours
            good_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 300 < area < 50000:
                    x, y, w, h = cv2.boundingRect(contour)
                    if (15 < w < 500 and 15 < h < 500 and 
                        max(w, h) / min(w, h) < 5.0 and
                        area / (w * h) > 0.3):
                        good_contours.append(contour)
            
            print(f"  ✓ Found {len(good_contours)} seeds")
            
            if len(good_contours) == 0:
                print("  ⚠️ No seeds detected")
                continue
            
            # Create species directory
            species_dir = output_dir / "crops" / f"species_{species_id}"
            species_dir.mkdir(parents=True, exist_ok=True)
            
            # Extract crops and create annotations
            base_name = img_path.stem
            bboxes = []
            saved_crops = []
            
            for j, contour in enumerate(good_contours):
                x, y, w, h = cv2.boundingRect(contour)
                bboxes.append((x, y, w, h))
                
                # Extract crop with padding
                pad = 10
                x1 = max(0, x - pad)
                y1 = max(0, y - pad)
                x2 = min(image.shape[1], x + w + pad)
                y2 = min(image.shape[0], y + h + pad)
                
                crop = image[y1:y2, x1:x2]
                
                # Save crop
                crop_name = f"{base_name}_seed_{j:03d}.jpg"
                crop_path = species_dir / crop_name
                
                success = cv2.imwrite(str(crop_path), crop)
                if success:
                    saved_crops.append(str(crop_path))
                    print(f"    ✓ Saved: {crop_name}")
                else:
                    print(f"    ❌ Failed: {crop_name}")
            
            # Create YOLO annotation
            h_img, w_img = image.shape[:2]
            yolo_lines = []
            for x, y, w, h in bboxes:
                center_x = (x + w/2) / w_img
                center_y = (y + h/2) / h_img
                norm_w = w / w_img
                norm_h = h / h_img
                yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_w:.6f} {norm_h:.6f}")
            
            # Save annotation
            ann_file = output_dir / "annotations" / f"{base_name}.txt"
            with open(ann_file, 'w') as f:
                f.write('\n'.join(yolo_lines))
            print(f"  ✓ Saved annotation: {ann_file.name}")
            
            # Create debug image
            debug_img = image.copy()
            cv2.drawContours(debug_img, good_contours, -1, (0, 255, 0), 2)
            for k, (x, y, w, h) in enumerate(bboxes):
                cv2.rectangle(debug_img, (x, y), (x+w, y+h), (255, 0, 0), 2)
                cv2.putText(debug_img, str(k), (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            
            debug_path = output_dir / "debug" / f"debug_{img_path.name}"
            cv2.imwrite(str(debug_path), debug_img)
            print(f"  ✓ Saved debug: {debug_path.name}")
            
            results.append({
                'filename': img_path.name,
                'species_id': species_id,
                'class_id': class_id,
                'seeds_found': len(good_contours),
                'saved_crops': saved_crops
            })
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    # Save metadata
    class_mapping = {
        'species_to_class': species_class_map,
        'class_to_species': {v: k for k, v in species_class_map.items()},
        'total_classes': len(species_class_map)
    }
    
    with open(output_dir / "class_mapping.json", 'w') as f:
        json.dump(class_mapping, f, indent=2)
    
    total_seeds = sum(r['seeds_found'] for r in results)
    summary = {
        'total_images': len(results),
        'total_seeds': total_seeds,
        'average_seeds_per_image': total_seeds / len(results) if results else 0,
        'results': results
    }
    
    with open(output_dir / "processing_summary.json", 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n🎉 PROCESSING COMPLETE!")
    print(f"✓ Images processed: {len(results)}")
    print(f"✓ Total seeds: {total_seeds}")
    print(f"✓ Species found: {len(species_class_map)}")
    
    # Verify crop files
    crop_files = list((output_dir / "crops").rglob("*.jpg"))
    print(f"✓ Crop files created: {len(crop_files)}")
    
    if crop_files:
        print("\n📸 Created crop files:")
        for crop in crop_files:
            rel_path = crop.relative_to(output_dir)
            print(f"  • {rel_path}")
    
    print(f"\n📁 Output saved to: {output_dir.absolute()}")

if __name__ == "__main__":
    main()
