#!/usr/bin/env python3
"""
SAM处理诊断脚本
用于诊断和修复"处理完成但无输出文件"的问题
"""

import sys
import os
from pathlib import Path
import json
import traceback

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    missing_packages = []
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        missing_packages.append("torch")
        print("❌ PyTorch 未安装")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        missing_packages.append("opencv-python")
        print("❌ OpenCV 未安装")
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError:
        missing_packages.append("numpy")
        print("❌ NumPy 未安装")
    
    try:
        from segment_anything import sam_model_registry, SamAutomaticMaskGenerator, SamPredictor
        print("✅ Segment Anything 已安装")
        sam_available = True
    except ImportError:
        missing_packages.append("segment-anything")
        print("❌ Segment Anything 未安装")
        sam_available = False
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("安装命令:")
        for pkg in missing_packages:
            if pkg == "segment-anything":
                print(f"  pip install {pkg}")
            else:
                print(f"  pip install {pkg}")
        return False
    
    return sam_available

def check_sam_model(model_path):
    """检查SAM模型文件"""
    print(f"\n🔍 检查SAM模型文件: {model_path}")
    
    model_file = Path(model_path)
    
    if not model_file.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请下载SAM模型文件:")
        print("  wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
        return False
    
    file_size = model_file.stat().st_size / (1024 * 1024 * 1024)  # GB
    print(f"✅ 模型文件存在，大小: {file_size:.2f} GB")
    
    if file_size < 2.0:
        print("⚠️ 模型文件可能不完整，标准大小应该约为2.6GB")
        return False
    
    return True

def check_input_directory(input_dir):
    """检查输入目录"""
    print(f"\n🔍 检查输入目录: {input_dir}")
    
    input_path = Path(input_dir)
    
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return False, []
    
    # 查找图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    print(f"✅ 输入目录存在，找到 {len(image_files)} 个图像文件")
    
    if len(image_files) == 0:
        print("❌ 输入目录中没有图像文件")
        return False, []
    
    # 显示前几个文件
    for i, img_file in enumerate(image_files[:5]):
        file_size = img_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  📷 {img_file.name} ({file_size:.1f} MB)")
    
    if len(image_files) > 5:
        print(f"  ... 还有 {len(image_files) - 5} 个文件")
    
    return True, image_files

def check_output_directory(output_dir):
    """检查输出目录权限"""
    print(f"\n🔍 检查输出目录权限: {output_dir}")
    
    output_path = Path(output_dir)
    
    try:
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 输出目录创建成功: {output_path}")
        
        # 测试写入权限
        test_file = output_path / "test_write_permission.txt"
        with open(test_file, 'w') as f:
            f.write("test")
        test_file.unlink()  # 删除测试文件
        print("✅ 输出目录写入权限正常")
        
        return True
        
    except PermissionError:
        print(f"❌ 输出目录权限不足: {output_dir}")
        return False
    except Exception as e:
        print(f"❌ 输出目录检查失败: {e}")
        return False

def test_sam_backend():
    """测试SAM后端"""
    print("\n🔍 测试SAM后端...")
    
    try:
        from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig, SAM_AVAILABLE
        print("✅ SAM后端模块导入成功")
        
        if not SAM_AVAILABLE:
            print("❌ SAM后端不可用")
            return False
        
        print("✅ SAM后端可用")
        return True
        
    except ImportError as e:
        print(f"❌ SAM后端导入失败: {e}")
        return False

def test_simple_sam_processing(model_path, input_dir, output_dir):
    """测试简单的SAM处理"""
    print("\n🧪 测试简单SAM处理...")
    
    try:
        from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig
        
        # 创建配置
        sam_config = SAMConfig(
            model_type="vit_h",
            checkpoint_path=model_path,
            device="cpu",  # 使用CPU避免GPU问题
            points_per_side=16,  # 减少点数加快测试
            pred_iou_thresh=0.88,
            stability_score_thresh=0.95,
            min_mask_region_area=100
        )
        
        processing_config = ProcessingConfig(
            input_directory=input_dir,
            output_directory=output_dir,
            preview_mode=True,
            max_preview_images=1,  # 只处理一个图像进行测试
            save_debug_images=True,
            create_yolo_annotations=True,
            min_seed_area=100,
            max_seed_area=50000,
            min_aspect_ratio=0.3,
            max_aspect_ratio=3.0,
            min_solidity=0.8
        )
        
        # 创建回调函数
        def progress_callback(progress_data):
            print(f"  进度: {progress_data['percentage']:.1f}% - {progress_data['current_image']}")
        
        def log_callback(message, level='info'):
            print(f"  [{level.upper()}] {message}")
        
        print("正在初始化SAM模型...")
        segmenter = SAMSeedSegmenter(
            sam_config=sam_config,
            processing_config=processing_config,
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        print("开始处理...")
        results = segmenter.process_directory()
        
        print(f"✅ 处理完成!")
        print(f"  处理图像: {results['total_images']}")
        print(f"  成功处理: {results['successful_images']}")
        print(f"  检测到种子: {results['total_seeds_found']}")
        print(f"  处理时间: {results['processing_time']:.1f}秒")
        
        # 检查输出文件
        output_path = Path(output_dir)
        crops_dir = output_path / "crops"
        debug_dir = output_path / "debug"
        annotations_dir = output_path / "annotations"
        
        crops_count = len(list(crops_dir.rglob("*.jpg"))) + len(list(crops_dir.rglob("*.png"))) if crops_dir.exists() else 0
        debug_count = len(list(debug_dir.glob("*.jpg"))) + len(list(debug_dir.glob("*.png"))) if debug_dir.exists() else 0
        annotation_count = len(list(annotations_dir.glob("*.txt"))) if annotations_dir.exists() else 0
        
        print(f"\n📊 输出文件检查:")
        print(f"  种子裁剪图像: {crops_count} 个")
        print(f"  调试图像: {debug_count} 个")
        print(f"  YOLO注释: {annotation_count} 个")
        
        if crops_count > 0 or debug_count > 0:
            print("✅ 成功生成输出文件!")
            return True
        else:
            print("❌ 未生成输出文件")
            return False
        
    except Exception as e:
        print(f"❌ SAM处理测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("SAM处理诊断工具")
    print("=" * 60)
    
    # 获取配置
    config_file = "sam_gui_config.json"
    model_path = "sam_vit_h_4b8939.pth"
    input_dir = "input_images"
    output_dir = "test_output"
    
    if Path(config_file).exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            model_path = config.get('model_path', model_path)
            input_dir = config.get('input_dir', input_dir)
            output_dir = config.get('output_dir', output_dir)
            print(f"📋 从配置文件加载设置: {config_file}")
        except Exception as e:
            print(f"⚠️ 配置文件读取失败: {e}")
    
    print(f"🔧 使用设置:")
    print(f"  SAM模型: {model_path}")
    print(f"  输入目录: {input_dir}")
    print(f"  输出目录: {output_dir}")
    
    # 运行诊断
    checks = [
        ("依赖包检查", lambda: check_dependencies()),
        ("SAM模型检查", lambda: check_sam_model(model_path)),
        ("输入目录检查", lambda: check_input_directory(input_dir)[0]),
        ("输出目录检查", lambda: check_output_directory(output_dir)),
        ("SAM后端检查", lambda: test_sam_backend()),
    ]
    
    passed = 0
    for check_name, check_func in checks:
        print(f"\n--- {check_name} ---")
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name} 通过")
            else:
                print(f"❌ {check_name} 失败")
        except Exception as e:
            print(f"❌ {check_name} 异常: {e}")
    
    print(f"\n📊 诊断结果: {passed}/{len(checks)} 项检查通过")
    
    if passed == len(checks):
        print("\n🧪 所有基础检查通过，开始SAM处理测试...")
        
        # 检查是否有输入图像
        success, image_files = check_input_directory(input_dir)
        if success and len(image_files) > 0:
            if test_simple_sam_processing(model_path, input_dir, output_dir):
                print("\n🎉 SAM处理测试成功！问题已修复。")
                return 0
            else:
                print("\n❌ SAM处理测试失败，需要进一步调试。")
                return 1
        else:
            print(f"\n⚠️ 请在 {input_dir} 目录中放置一些测试图像，然后重新运行诊断。")
            return 1
    else:
        print("\n❌ 基础检查失败，请先解决上述问题。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
