@echo off
chcp 65001 >nul
title SAM种子分割工具 - SAM Seed Segmentation Tool

echo.
echo ========================================
echo    SAM种子分割工具 GUI启动器
echo    SAM Seed Segmentation Tool GUI
echo ========================================
echo.

echo 正在检查Python环境...
echo Checking Python environment...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo ❌ Python not installed or not in PATH
    echo.
    echo 请安装Python 3.8或更高版本
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo ✅ Python environment check passed

echo.
echo 正在启动SAM GUI...
echo Starting SAM GUI...
echo.

python launch_sam_gui.py

if errorlevel 1 (
    echo.
    echo ❌ GUI启动失败
    echo ❌ GUI launch failed
    echo.
    echo 可能的解决方案:
    echo Possible solutions:
    echo 1. 安装依赖: pip install segment-anything opencv-python numpy matplotlib pillow
    echo 2. 下载SAM模型文件: sam_vit_h_4b8939.pth
    echo 3. 检查文件完整性
    echo.
    pause
    exit /b 1
)

echo.
echo GUI已关闭
echo GUI closed
pause
