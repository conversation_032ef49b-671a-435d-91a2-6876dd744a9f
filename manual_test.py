#!/usr/bin/env python3
"""
Manual test script to process seed images and create the required output structure.
This script will process 3 sample images and demonstrate the full functionality.
"""

import os
import cv2
import numpy as np
import json
from pathlib import Path
import sys

def create_output_directories():
    """Create the required output directory structure."""
    output_dir = Path("./output")
    
    # Create main directories
    dirs_to_create = [
        output_dir,
        output_dir / "crops",
        output_dir / "annotations", 
        output_dir / "debug"
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {dir_path}")
    
    return output_dir

def extract_species_from_filename(filename):
    """Extract species ID from filename using pattern S(\d+)-"""
    import re
    match = re.search(r'S(\d+)-', filename)
    if match:
        return match.group(1)
    return "unknown"

def segment_seeds_simple(image):
    """Simple seed segmentation using basic computer vision techniques."""
    # Convert to grayscale
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    # Apply Gaussian blur
    blurred = cv2.GaussianBlur(gray, (5, 5), 1.0)
    
    # Apply adaptive thresholding
    binary = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY, 11, 2
    )
    
    # Morphological operations to clean up
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # Find contours
    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours by size and shape
    filtered_contours = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if 300 < area < 50000:  # Reasonable seed size range
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check aspect ratio and size
            aspect_ratio = max(w, h) / min(w, h)
            if (aspect_ratio < 5.0 and w > 15 and h > 15 and 
                w < 500 and h < 500):
                
                # Check extent (how much of bounding box is filled)
                extent = area / (w * h)
                if extent > 0.3:
                    filtered_contours.append(contour)
    
    return filtered_contours, cleaned

def create_yolo_annotation(image_shape, bounding_boxes, class_id):
    """Create YOLO format annotation string."""
    height, width = image_shape
    annotations = []
    
    for x, y, w, h in bounding_boxes:
        # Convert to YOLO format (normalized center coordinates)
        center_x = (x + w / 2) / width
        center_y = (y + h / 2) / height
        norm_width = w / width
        norm_height = h / height
        
        annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}")
    
    return "\n".join(annotations)

def process_single_image(image_path, output_dir, species_class_map):
    """Process a single image and extract seeds."""
    try:
        print(f"\nProcessing: {os.path.basename(image_path)}")
        
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"  ✗ Could not load image")
            return None
        
        filename = os.path.basename(image_path)
        base_name = Path(filename).stem
        
        # Extract species ID
        species_id = extract_species_from_filename(filename)
        print(f"  Species ID: {species_id}")
        
        # Get or create class ID
        if species_id not in species_class_map:
            species_class_map[species_id] = len(species_class_map)
        class_id = species_class_map[species_id]
        
        # Segment seeds
        contours, binary_image = segment_seeds_simple(image)
        print(f"  Found {len(contours)} potential seeds")
        
        if len(contours) == 0:
            print(f"  ⚠ No seeds detected")
            return None
        
        # Create species directory
        species_dir = output_dir / "crops" / f"species_{species_id}"
        species_dir.mkdir(parents=True, exist_ok=True)
        
        # Extract and save individual seeds
        bounding_boxes = []
        saved_crops = []
        
        for i, contour in enumerate(contours):
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            bounding_boxes.append((x, y, w, h))
            
            # Extract crop with padding
            padding = 5
            x_pad = max(0, x - padding)
            y_pad = max(0, y - padding)
            w_pad = min(image.shape[1] - x_pad, w + 2 * padding)
            h_pad = min(image.shape[0] - y_pad, h + 2 * padding)
            
            crop = image[y_pad:y_pad + h_pad, x_pad:x_pad + w_pad]
            
            # Save crop
            crop_filename = f"{base_name}_seed_{i:03d}.jpg"
            crop_path = species_dir / crop_filename
            cv2.imwrite(str(crop_path), crop)
            saved_crops.append(str(crop_path))
            print(f"    Saved: {crop_filename}")
        
        # Create YOLO annotation
        yolo_annotation = create_yolo_annotation(
            (image.shape[0], image.shape[1]), 
            bounding_boxes, 
            class_id
        )
        
        # Save annotation file
        annotation_file = output_dir / "annotations" / f"{base_name}.txt"
        with open(annotation_file, 'w') as f:
            f.write(yolo_annotation)
        print(f"  Saved annotation: {annotation_file.name}")
        
        # Create debug image
        debug_image = image.copy()
        cv2.drawContours(debug_image, contours, -1, (0, 255, 0), 2)
        
        # Draw bounding rectangles
        for x, y, w, h in bounding_boxes:
            cv2.rectangle(debug_image, (x, y), (x + w, y + h), (255, 0, 0), 1)
            # Add seed number
            cv2.putText(debug_image, f"{len([b for b in bounding_boxes if b == (x, y, w, h)])}", 
                       (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
        
        # Save debug image
        debug_path = output_dir / "debug" / f"debug_{filename}"
        cv2.imwrite(str(debug_path), debug_image)
        print(f"  Saved debug image: {debug_path.name}")
        
        return {
            'filename': filename,
            'species_id': species_id,
            'class_id': class_id,
            'seeds_found': len(contours),
            'saved_crops': saved_crops,
            'annotation_file': str(annotation_file),
            'debug_image': str(debug_path),
            'yolo_annotation': yolo_annotation
        }
        
    except Exception as e:
        print(f"  ✗ Error processing {image_path}: {str(e)}")
        return None

def main():
    """Main function to run the test."""
    print("=" * 60)
    print("SEED CLASSIFICATION TOOL - MANUAL TEST")
    print("=" * 60)
    
    # Create output directories
    output_dir = create_output_directories()
    
    # Find sample images to process
    input_dir = Path("CVH-seed-pic/CVH-seed-pic")
    if not input_dir.exists():
        print(f"✗ Input directory not found: {input_dir}")
        return
    
    # Get first 3 JPG files for testing
    image_files = list(input_dir.glob("*.jpg"))[:3]
    if not image_files:
        print("✗ No JPG files found")
        return
    
    print(f"\nFound {len(image_files)} images to process:")
    for img_file in image_files:
        print(f"  - {img_file.name}")
    
    # Process images
    species_class_map = {}
    results = []
    total_seeds = 0
    
    for image_path in image_files:
        result = process_single_image(image_path, output_dir, species_class_map)
        if result:
            results.append(result)
            total_seeds += result['seeds_found']
    
    # Save class mapping
    class_mapping = {
        'species_to_class': species_class_map,
        'class_to_species': {v: k for k, v in species_class_map.items()},
        'total_classes': len(species_class_map)
    }
    
    mapping_file = output_dir / "class_mapping.json"
    with open(mapping_file, 'w') as f:
        json.dump(class_mapping, f, indent=2)
    print(f"\nSaved class mapping: {mapping_file}")
    
    # Create processing summary
    summary = {
        'total_images_processed': len(results),
        'total_seeds_extracted': total_seeds,
        'average_seeds_per_image': total_seeds / len(results) if results else 0,
        'species_found': list(species_class_map.keys()),
        'results': results
    }
    
    summary_file = output_dir / "processing_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"Saved processing summary: {summary_file}")
    
    # Print final results
    print("\n" + "=" * 60)
    print("✅ PROCESSING COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print(f"Images processed: {len(results)}")
    print(f"Total seeds extracted: {total_seeds}")
    print(f"Average seeds per image: {total_seeds / len(results) if results else 0:.1f}")
    print(f"Species found: {len(species_class_map)}")
    
    if species_class_map:
        print("\nSpecies summary:")
        for species_id, class_id in species_class_map.items():
            species_seeds = sum(r['seeds_found'] for r in results if r['species_id'] == species_id)
            print(f"  Species {species_id} (class {class_id}): {species_seeds} seeds")
    
    # Show output structure
    print(f"\nOutput structure created in: {output_dir.absolute()}")
    print("📁 output/")
    print("  ├── 📁 crops/")
    for species_id in species_class_map.keys():
        species_dir = output_dir / "crops" / f"species_{species_id}"
        if species_dir.exists():
            crop_count = len(list(species_dir.glob("*.jpg")))
            print(f"  │   └── 📁 species_{species_id}/ ({crop_count} seed images)")
    print("  ├── 📁 annotations/ (YOLO format files)")
    print("  ├── 📁 debug/ (visualization images)")
    print("  ├── 📄 class_mapping.json")
    print("  └── 📄 processing_summary.json")
    
    # Show sample annotation content
    if results:
        sample_result = results[0]
        print(f"\nSample YOLO annotation for {sample_result['filename']}:")
        annotation_lines = sample_result['yolo_annotation'].split('\n')
        for i, line in enumerate(annotation_lines[:3]):  # Show first 3 detections
            print(f"  {line}")
        if len(annotation_lines) > 3:
            print(f"  ... and {len(annotation_lines) - 3} more detections")
    
    print(f"\n🎉 Test completed! Check the output directory for results.")

if __name__ == "__main__":
    main()
