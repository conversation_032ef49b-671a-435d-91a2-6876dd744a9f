#!/usr/bin/env python3
"""
Execute the seed processing and create real crop files.
This will run the processing and show the results.
"""

# Import required modules
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def run_processing():
    """Run the seed processing."""
    try:
        # Import the processing function
        from direct_processing import main as process_main
        
        print("Starting seed processing...")
        process_main()
        print("Processing completed!")
        
        # Verify results
        output_dir = Path("output")
        if output_dir.exists():
            crop_files = list((output_dir / "crops").rglob("*.jpg"))
            ann_files = list((output_dir / "annotations").glob("*.txt"))
            debug_files = list((output_dir / "debug").glob("*.jpg"))
            
            print(f"\nResults verification:")
            print(f"✓ Crop files: {len(crop_files)}")
            print(f"✓ Annotation files: {len(ann_files)}")
            print(f"✓ Debug files: {len(debug_files)}")
            
            if crop_files:
                print(f"\nSample crop files:")
                for crop in crop_files[:5]:
                    print(f"  • {crop.relative_to(output_dir)}")
            
            return True
        else:
            print("❌ Output directory not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_processing()
    if success:
        print("\n🎉 Seed processing completed successfully!")
    else:
        print("\n❌ Seed processing failed!")
