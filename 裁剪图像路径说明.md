# 种子裁剪图像路径说明
# Seed Crop Image Path Guide

## 📁 **裁剪图像的完整路径结构**

### **主要存储位置：**
```
当前工作目录/
└── output/                         # 输出根目录
    └── crops/                      # 裁剪图像主目录 ⭐ 这里是您要找的位置
        ├── species_0000003/        # 物种 0000003 的裁剪图像
        │   ├── S0000003-1_seed_000.jpg  # 第1个种子裁剪图像
        │   ├── S0000003-1_seed_001.jpg  # 第2个种子裁剪图像
        │   ├── S0000003-1_seed_002.jpg  # 第3个种子裁剪图像
        │   └── S0000003-1_seed_003.jpg  # 第4个种子裁剪图像
        ├── species_0000005/        # 物种 0000005 的裁剪图像
        │   ├── S0000005-1_seed_000.jpg  # 第1个种子裁剪图像
        │   ├── S0000005-1_seed_001.jpg  # 第2个种子裁剪图像
        │   └── S0000005-1_seed_002.jpg  # 第3个种子裁剪图像
        └── species_0000009/        # 物种 0000009 的裁剪图像
            ├── S0000009-1_seed_000.jpg  # 第1个种子裁剪图像
            ├── S0000009-1_seed_001.jpg  # 第2个种子裁剪图像
            ├── S0000009-1_seed_002.jpg  # 第3个种子裁剪图像
            ├── S0000009-1_seed_003.jpg  # 第4个种子裁剪图像
            └── S0000009-1_seed_004.jpg  # 第5个种子裁剪图像
```

## 🎯 **具体路径说明**

### **绝对路径格式：**
```
D:\昆明植物所\zhongzi\output\crops\species_{物种ID}\{原始文件名}_seed_{编号}.jpg
```

### **相对路径格式：**
```
./output/crops/species_{物种ID}/{原始文件名}_seed_{编号}.jpg
```

### **示例路径：**
1. **物种 0000003 的裁剪图像：**
   - `./output/crops/species_0000003/S0000003-1_seed_000.jpg`
   - `./output/crops/species_0000003/S0000003-1_seed_001.jpg`
   - `./output/crops/species_0000003/S0000003-1_seed_002.jpg`
   - `./output/crops/species_0000003/S0000003-1_seed_003.jpg`

2. **物种 0000005 的裁剪图像：**
   - `./output/crops/species_0000005/S0000005-1_seed_000.jpg`
   - `./output/crops/species_0000005/S0000005-1_seed_001.jpg`
   - `./output/crops/species_0000005/S0000005-1_seed_002.jpg`

3. **物种 0000009 的裁剪图像：**
   - `./output/crops/species_0000009/S0000009-1_seed_000.jpg`
   - `./output/crops/species_0000009/S0000009-1_seed_001.jpg`
   - `./output/crops/species_0000009/S0000009-1_seed_002.jpg`
   - `./output/crops/species_0000009/S0000009-1_seed_003.jpg`
   - `./output/crops/species_0000009/S0000009-1_seed_004.jpg`

## 🔍 **如何访问裁剪图像**

### **方法1：直接浏览文件夹**
1. 打开文件资源管理器
2. 导航到项目目录：`D:\昆明植物所\zhongzi\`
3. 进入 `output` 文件夹
4. 进入 `crops` 文件夹
5. 选择对应的物种文件夹（如 `species_0000003`）
6. 查看裁剪的种子图像

### **方法2：使用Python代码访问**
```python
from pathlib import Path

# 获取所有裁剪图像
crops_dir = Path("output/crops")
all_crops = list(crops_dir.rglob("*.jpg"))

for crop_file in all_crops:
    print(f"裁剪图像路径: {crop_file}")
```

### **方法3：使用命令行查看**
```bash
# 查看所有裁剪图像
dir output\crops\*\*.jpg /s

# 或者使用 PowerShell
Get-ChildItem -Path "output\crops" -Recurse -Filter "*.jpg"
```

## 📊 **文件命名规则**

### **命名格式：**
```
{原始图像文件名}_seed_{种子编号:03d}.jpg
```

### **命名示例：**
- `S0000003-1_seed_000.jpg` ← 来自 S0000003-1.jpg 的第1个种子
- `S0000003-1_seed_001.jpg` ← 来自 S0000003-1.jpg 的第2个种子
- `S0000005-1_seed_000.jpg` ← 来自 S0000005-1.jpg 的第1个种子

### **编号说明：**
- 种子编号从 `000` 开始
- 使用3位数字格式（001, 002, 003...）
- 按检测顺序编号

## 🏷️ **物种目录说明**

### **目录命名格式：**
```
species_{物种ID}
```

### **物种ID提取规则：**
- 从原始文件名中提取：`S(\d+)-` 模式
- 例如：`S0000003-1.jpg` → 物种ID: `0000003`
- 例如：`S0000005-1.jpg` → 物种ID: `0000005`

## 🎯 **快速定位裁剪图像**

### **最重要的路径：**
```
📁 output/crops/  ← 这是您要找的主要位置！
```

### **检查是否生成成功：**
1. 确认 `output/crops/` 目录存在
2. 确认物种子目录存在（如 `species_0000003/`）
3. 确认JPG文件存在（如 `S0000003-1_seed_000.jpg`）

## 🚀 **生成裁剪图像的命令**

如果裁剪图像还未生成，请运行：

```bash
# 预览模式（处理少量图像）
python seed_classifier.py "CVH-seed-pic" "./output" --preview --debug

# 完整处理模式
python seed_classifier.py "CVH-seed-pic" "./output" --debug
```

## 📝 **注意事项**

1. **文件格式：** 所有裁剪图像都是JPG格式
2. **图像质量：** 包含检测到的种子及周围适当边距
3. **自动组织：** 按物种ID自动分类到不同文件夹
4. **唯一命名：** 每个裁剪图像都有唯一的文件名
5. **坐标记录：** 对应的YOLO注释文件记录了原始坐标信息

---

**总结：裁剪处理完的图像主要存储在 `output/crops/` 目录下，按物种分类存放。** 🌱
