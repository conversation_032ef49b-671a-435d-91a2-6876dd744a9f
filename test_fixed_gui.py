#!/usr/bin/env python3
"""
Simple test for the fixed advanced GUI
"""

import sys
import tkinter as tk

def test_fixed_gui():
    """Test the fixed GUI"""
    try:
        print("Testing fixed advanced GUI...")
        
        # Test import
        from sam_gui_advanced_fixed import SAMGUIAdvancedFixed, LanguageManager, SessionManager
        print("✅ Fixed GUI classes imported successfully")
        
        # Test language manager
        lang_mgr = LanguageManager()
        print(f"✅ Language manager created, current language: {lang_mgr.current_language}")
        
        # Test session manager
        session_mgr = SessionManager("test_output")
        print("✅ Session manager created")
        
        # Test GUI creation (without showing)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        app = SAMGUIAdvancedFixed(root)
        print("✅ Fixed GUI application created successfully")
        
        # Test basic functionality
        app.create_new_session()
        print("✅ Session creation works")
        
        app.change_language('zh')
        print("✅ Language change works")
        
        app.log_message("Test message")
        print("✅ Logging works")
        
        root.destroy()
        print("✅ GUI cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 50)
    print("Fixed Advanced GUI Test")
    print("=" * 50)
    
    if test_fixed_gui():
        print("\n🎉 Fixed GUI test passed!")
        print("\nTo launch the fixed GUI, run:")
        print("python sam_gui_advanced_fixed.py")
        print("or")
        print("python launch_sam_gui.py")
        return 0
    else:
        print("\n❌ Fixed GUI test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
