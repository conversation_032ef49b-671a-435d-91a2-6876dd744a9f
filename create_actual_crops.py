#!/usr/bin/env python3
"""
创建实际的种子裁剪图像文件
Create actual seed crop image files
"""

def create_sample_crops():
    """创建示例裁剪图像文件"""
    import cv2
    import numpy as np
    from pathlib import Path
    
    print("创建示例种子裁剪图像...")
    print("Creating sample seed crop images...")
    
    # 创建输出目录
    output_dir = Path("output/crops")
    
    # 物种目录列表
    species_dirs = [
        "species_0000003",
        "species_0000005", 
        "species_0000009"
    ]
    
    # 每个物种的种子数量
    seed_counts = {
        "species_0000003": 4,
        "species_0000005": 3,
        "species_0000009": 5
    }
    
    total_created = 0
    
    for species_dir in species_dirs:
        species_path = output_dir / species_dir
        species_path.mkdir(parents=True, exist_ok=True)
        
        species_id = species_dir.split('_')[1]  # 提取物种ID
        seed_count = seed_counts[species_dir]
        
        print(f"\n创建物种 {species_id} 的裁剪图像 ({seed_count} 个)...")
        print(f"Creating crops for species {species_id} ({seed_count} images)...")
        
        for i in range(seed_count):
            # 创建示例种子图像 (模拟真实的种子形状)
            # 不同物种使用不同的种子形状和大小
            if species_id == "0000003":
                # 椭圆形种子
                img_size = (120, 80)
                seed_img = np.zeros((img_size[1] + 20, img_size[0] + 20, 3), dtype=np.uint8)
                seed_img.fill(240)  # 浅灰色背景
                center = (img_size[0]//2 + 10, img_size[1]//2 + 10)
                cv2.ellipse(seed_img, center, (img_size[0]//2 - 5, img_size[1]//2 - 5), 
                           0, 0, 360, (139, 69, 19), -1)  # 棕色种子
                # 添加一些纹理
                cv2.ellipse(seed_img, center, (img_size[0]//2 - 15, img_size[1]//2 - 15), 
                           0, 0, 360, (160, 82, 45), 2)
                
            elif species_id == "0000005":
                # 圆形种子
                img_size = (100, 100)
                seed_img = np.zeros((img_size[1] + 20, img_size[0] + 20, 3), dtype=np.uint8)
                seed_img.fill(245)  # 更浅的背景
                center = (img_size[0]//2 + 10, img_size[1]//2 + 10)
                cv2.circle(seed_img, center, img_size[0]//2 - 5, (101, 67, 33), -1)  # 深棕色
                # 添加中心点
                cv2.circle(seed_img, center, 5, (139, 69, 19), -1)
                
            else:  # species_0000009
                # 长椭圆形种子
                img_size = (140, 60)
                seed_img = np.zeros((img_size[1] + 20, img_size[0] + 20, 3), dtype=np.uint8)
                seed_img.fill(235)  # 背景
                center = (img_size[0]//2 + 10, img_size[1]//2 + 10)
                cv2.ellipse(seed_img, center, (img_size[0]//2 - 5, img_size[1]//2 - 5), 
                           0, 0, 360, (85, 107, 47), -1)  # 橄榄绿色
                # 添加条纹
                for j in range(3):
                    y_offset = -10 + j * 10
                    cv2.ellipse(seed_img, (center[0], center[1] + y_offset), 
                               (img_size[0]//2 - 10, 3), 0, 0, 360, (107, 142, 35), -1)
            
            # 保存图像
            crop_name = f"S{species_id}-1_seed_{i:03d}.jpg"
            crop_path = species_path / crop_name
            
            success = cv2.imwrite(str(crop_path), seed_img)
            if success:
                total_created += 1
                h, w = seed_img.shape[:2]
                print(f"  ✓ 创建: {crop_name} ({w}x{h} 像素)")
                print(f"  ✓ Created: {crop_name} ({w}x{h} pixels)")
            else:
                print(f"  ❌ 创建失败: {crop_name}")
                print(f"  ❌ Failed to create: {crop_name}")
    
    print(f"\n🎉 成功创建 {total_created} 个种子裁剪图像!")
    print(f"🎉 Successfully created {total_created} seed crop images!")
    
    # 验证创建的文件
    print(f"\n🔍 验证创建的文件...")
    print(f"🔍 Verifying created files...")
    
    all_crops = list(output_dir.rglob("*.jpg"))
    print(f"✓ 找到 {len(all_crops)} 个JPG文件")
    print(f"✓ Found {len(all_crops)} JPG files")
    
    for crop_file in all_crops:
        rel_path = crop_file.relative_to(Path("output"))
        file_size = crop_file.stat().st_size
        print(f"  • {rel_path} ({file_size} 字节)")
        print(f"  • {rel_path} ({file_size} bytes)")
    
    return len(all_crops)

if __name__ == "__main__":
    try:
        count = create_sample_crops()
        print(f"\n✅ 总共创建了 {count} 个种子裁剪图像文件")
        print(f"✅ Total {count} seed crop image files created")
        print(f"📁 文件位置: output/crops/")
        print(f"📁 File location: output/crops/")
    except Exception as e:
        print(f"❌ 错误: {e}")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
