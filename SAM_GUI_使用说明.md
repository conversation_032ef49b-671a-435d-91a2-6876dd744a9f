# SAM种子分割工具GUI使用说明
# SAM Seed Segmentation Tool GUI User Guide

## 🎯 **GUI工具概述**

基于Meta的Segment Anything Model (SAM) 的图形化种子分割工具，提供用户友好的界面来管理高精度种子分割和裁剪任务。

### **主要特点:**
- ✅ **直观的图形界面** - 无需命令行操作
- ✅ **实时进度监控** - 处理进度、速度、ETA显示
- ✅ **预设配置** - 快速应用最佳参数组合
- ✅ **多线程处理** - 界面响应流畅，支持暂停/恢复
- ✅ **配置管理** - 保存/加载自定义配置
- ✅ **中英文界面** - 双语支持

## 📁 **文件结构**

```
SAM种子分割工具/
├── sam_gui.py                    # 基础GUI程序
├── sam_gui_enhanced.py           # 增强版GUI程序 (推荐)
├── launch_sam_gui.py             # GUI启动器
├── sam_seed_segmenter.py         # 后端处理模块
├── sam_gui_config.json           # GUI配置文件
├── sam_vit_h_4b8939.pth         # SAM模型文件 (需下载)
├── SAM_GUI_使用说明.md           # 本文档
└── requirements_sam.txt          # Python依赖
```

## 🚀 **快速开始**

### **步骤1: 环境准备**
```bash
# 安装依赖
pip install segment-anything opencv-python numpy matplotlib pillow

# 或使用自动安装脚本
python install_sam_dependencies.py
```

### **步骤2: 下载SAM模型**
下载 `sam_vit_h_4b8939.pth` (约2.4GB) 到工具目录:
- **官方链接:** https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
- **GitHub:** https://github.com/facebookresearch/segment-anything#model-checkpoints

### **步骤3: 启动GUI**
```bash
# 推荐：使用启动器（自动检查环境）
python launch_sam_gui.py

# 或直接启动增强版GUI
python sam_gui_enhanced.py

# 或启动基础版GUI
python sam_gui.py
```

## 🖥️ **界面详解**

### **标签页1: 文件设置 / File Settings**

#### **路径配置:**
- **输入图像目录** - 包含种子图像的文件夹
- **输出目录** - 处理结果保存位置
- **SAM模型文件** - sam_vit_h_4b8939.pth文件路径

#### **设备设置:**
- **auto** - 自动检测（推荐）
- **cpu** - 使用CPU（兼容性好）
- **cuda** - 使用GPU（需要CUDA支持）

#### **预览设置:**
- **预览模式** - 只处理前几张图像进行测试
- **最大预览数** - 预览模式下处理的图像数量

#### **输出选项:**
- **保存调试图像** - 生成可视化调试图像
- **创建YOLO注释** - 生成目标检测训练用的注释文件

### **标签页2: 参数设置 / Parameters**

#### **SAM模型参数:**
- **采样点密度 (Points per Side):** 8-64
  - 控制分割精度，值越大越精细但速度越慢
  - 推荐：快速=16, 平衡=32, 高精度=64

- **IoU阈值 (IoU Threshold):** 0.5-1.0
  - 控制掩码质量，值越高质量越好
  - 推荐：0.85-0.92

- **稳定性阈值 (Stability Threshold):** 0.5-1.0
  - 过滤不稳定的分割结果
  - 推荐：0.90-0.97

#### **种子过滤参数:**
- **种子面积范围** - 根据实际种子大小设置
  - 小种子：200-50,000像素
  - 大种子：2,000-1,000,000像素

- **纵横比范围** - 适应不同种子形状
  - 圆形种子：0.5-2.0
  - 椭圆种子：0.2-5.0

- **最小凸度** - 确保检测实心对象
  - 推荐：0.3-0.8

### **标签页3: 处理控制 / Processing Control**

#### **控制按钮:**
- **开始处理** - 启动分割任务
- **暂停/恢复** - 暂停或恢复处理
- **停止** - 终止处理任务

#### **进度显示:**
- **总体进度条** - 显示完成百分比
- **当前图像** - 正在处理的图像文件名
- **处理统计** - 已处理/总数、检测种子数
- **处理速度** - 图像/秒
- **预计剩余时间** - ETA

#### **处理日志:**
- **实时日志** - 显示处理过程和结果
- **日志控制** - 清空日志、保存日志到文件

### **标签页4: 结果预览 / Preview**

#### **图像预览:**
- **可视化显示** - 当前处理图像和检测结果
- **预览控制** - 刷新预览、打开输出目录

#### **处理结果:**
- **结果统计** - 详细的处理报告
- **输出文件** - 生成的文件列表和路径

## ⚙️ **预设配置**

### **内置预设:**

1. **高精度模式 / High Precision**
   - Points per Side: 64
   - IoU Threshold: 0.92
   - Stability Threshold: 0.97
   - 适用：科研级别的精确分割

2. **平衡模式 / Balanced** (推荐)
   - Points per Side: 32
   - IoU Threshold: 0.88
   - Stability Threshold: 0.95
   - 适用：日常使用的最佳选择

3. **快速模式 / Fast**
   - Points per Side: 16
   - IoU Threshold: 0.85
   - Stability Threshold: 0.90
   - 适用：大批量处理

4. **大种子模式 / Large Seeds**
   - Min Area: 2,000
   - Max Area: 1,000,000
   - 适用：大型种子图像

5. **小种子模式 / Small Seeds**
   - Min Area: 200
   - Max Area: 50,000
   - 适用：小型种子图像

### **应用预设:**
通过菜单栏 "预设 / Presets" → 选择对应模式

## 📊 **输出结果**

### **文件结构:**
```
输出目录/
├── crops/                    # 种子裁剪图像
│   ├── species_0000012/     # 按物种分组
│   │   ├── S0000012-1_seed_000.jpg
│   │   ├── S0000012-1_seed_001.jpg
│   │   └── ...
│   └── species_0000013/
├── annotations/             # YOLO格式注释
│   ├── S0000012-1.txt
│   └── ...
├── debug/                   # 调试图像
│   ├── sam_debug_S0000012-1.jpg
│   └── ...
├── logs/                    # 处理日志
└── sam_processing_report.json  # 详细报告
```

### **质量检查:**
1. 查看 `debug/` 目录中的可视化图像
2. 检查 `crops/` 目录中的裁剪质量
3. 验证 `annotations/` 中的YOLO注释

## 🔧 **配置管理**

### **保存配置:**
1. 调整参数到满意状态
2. 点击 "保存配置 / Save Config"
3. 选择保存位置和文件名

### **加载配置:**
1. 点击 "加载配置 / Load Config"
2. 选择之前保存的配置文件
3. 参数自动应用到界面

### **重置配置:**
点击 "重置配置 / Reset Config" 恢复默认设置

## 🐛 **故障排除**

### **常见问题:**

1. **启动失败**
   ```
   解决方案：
   - 运行 python launch_sam_gui.py 检查环境
   - 确保所有依赖已安装
   - 检查Python版本 (需要3.8+)
   ```

2. **模型文件错误**
   ```
   解决方案：
   - 确认sam_vit_h_4b8939.pth文件存在
   - 检查文件大小约2.4GB
   - 重新下载模型文件
   ```

3. **内存不足**
   ```
   解决方案：
   - 设备选择改为 "cpu"
   - 启用预览模式减少处理量
   - 关闭其他程序释放内存
   ```

4. **处理速度慢**
   ```
   解决方案：
   - 使用GPU模式 (device="cuda")
   - 降低采样点密度
   - 使用快速模式预设
   ```

5. **检测效果差**
   ```
   解决方案：
   - 调整种子过滤参数
   - 提高SAM模型参数
   - 使用高精度模式预设
   ```

## 📈 **性能优化**

### **速度优化:**
- 使用GPU加速
- 降低采样点密度
- 启用预览模式测试参数
- 批量处理时分批进行

### **质量优化:**
- 提高IoU和稳定性阈值
- 增加采样点密度
- 精确调整过滤参数
- 使用高精度预设

### **内存优化:**
- 使用CPU模式
- 减少批处理大小
- 关闭调试图像保存
- 定期清理输出目录

## 🎉 **最佳实践**

1. **首次使用:**
   - 启用预览模式
   - 使用平衡模式预设
   - 处理2-3张图像测试效果

2. **参数调优:**
   - 根据种子大小调整面积范围
   - 根据种子形状调整纵横比
   - 观察调试图像调整SAM参数

3. **批量处理:**
   - 确认参数后关闭预览模式
   - 监控处理日志
   - 定期检查输出质量

4. **结果验证:**
   - 检查调试图像确认检测效果
   - 验证裁剪图像质量
   - 统计检测准确率

现在您可以通过直观的图形界面享受专业级别的种子分割体验！🌱✨
