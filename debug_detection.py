#!/usr/bin/env python3
"""
直接调试种子检测问题
Direct debugging of seed detection issues
"""

import cv2
import numpy as np
from pathlib import Path
import json

def debug_single_image():
    """调试单个图像的处理过程"""
    print("🔍 直接调试种子检测")
    print("🔍 Direct debugging seed detection")
    print("="*50)
    
    # 测试图像
    image_path = "CVH-seed-pic/S0000012-1.jpg"
    
    if not Path(image_path).exists():
        print(f"❌ 图像不存在: {image_path}")
        return False
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法加载图像: {image_path}")
        return False
    
    print(f"✅ 图像加载成功: {image.shape[1]}x{image.shape[0]}")
    
    # 步骤1: 预处理
    print("\n🔧 步骤1: 预处理")
    
    # 多通道融合
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l_channel = lab[:, :, 0]
    
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v_channel = hsv[:, :, 2]
    
    gray_std = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 计算对比度
    l_contrast = cv2.Laplacian(l_channel, cv2.CV_64F).var()
    v_contrast = cv2.Laplacian(v_channel, cv2.CV_64F).var()
    gray_contrast = cv2.Laplacian(gray_std, cv2.CV_64F).var()
    
    print(f"  L通道对比度: {l_contrast:.2f}")
    print(f"  V通道对比度: {v_contrast:.2f}")
    print(f"  灰度对比度: {gray_contrast:.2f}")
    
    # 动态权重
    total_contrast = l_contrast + v_contrast + gray_contrast
    if total_contrast > 0:
        l_weight = max(0.3, min(0.7, l_contrast / total_contrast))
        v_weight = max(0.1, min(0.4, v_contrast / total_contrast))
        gray_weight = 1.0 - l_weight - v_weight
    else:
        l_weight, v_weight, gray_weight = 0.5, 0.3, 0.2
    
    print(f"  权重分配: L={l_weight:.2f}, V={v_weight:.2f}, Gray={gray_weight:.2f}")
    
    # 融合通道
    gray = cv2.addWeighted(
        cv2.addWeighted(l_channel, l_weight, v_channel, v_weight, 0),
        1.0, gray_std, gray_weight, 0
    )
    
    # 自适应CLAHE
    mean_intensity = np.mean(gray)
    print(f"  平均亮度: {mean_intensity:.1f}")
    
    if mean_intensity < 100:
        clip_limit = 4.0
        tile_size = (6, 6)
        bg_type = "暗背景"
    elif mean_intensity > 180:
        clip_limit = 2.0
        tile_size = (10, 10)
        bg_type = "亮背景"
    else:
        clip_limit = 3.0
        tile_size = (8, 8)
        bg_type = "中等背景"
    
    print(f"  背景类型: {bg_type}")
    print(f"  CLAHE参数: clip_limit={clip_limit}, tile_size={tile_size}")
    
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_size)
    enhanced = clahe.apply(gray.astype(np.uint8))
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(enhanced, (15, 15), 5.0)
    
    cv2.imwrite("debug_step1_preprocessed.jpg", blurred)
    print("  ✅ 预处理完成，保存到 debug_step1_preprocessed.jpg")
    
    # 步骤2: 阈值处理
    print("\n🔧 步骤2: 阈值处理")
    
    # 正向Otsu
    _, binary_normal = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    foreground_normal = np.sum(binary_normal == 255) / binary_normal.size
    
    # 反向Otsu
    _, binary_inv = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    foreground_inv = np.sum(binary_inv == 255) / binary_inv.size
    
    print(f"  正向Otsu前景比例: {foreground_normal:.3f}")
    print(f"  反向Otsu前景比例: {foreground_inv:.3f}")
    
    # 选择最佳阈值
    if 0.1 <= foreground_normal <= 0.6:
        binary = binary_normal
        chosen = "正向Otsu"
    elif 0.1 <= foreground_inv <= 0.6:
        binary = binary_inv
        chosen = "反向Otsu"
    else:
        if abs(foreground_normal - 0.3) < abs(foreground_inv - 0.3):
            binary = binary_normal
            chosen = "正向Otsu (更接近30%)"
        else:
            binary = binary_inv
            chosen = "反向Otsu (更接近30%)"
    
    print(f"  选择: {chosen}")
    
    cv2.imwrite("debug_step2_binary.jpg", binary)
    print("  ✅ 阈值处理完成，保存到 debug_step2_binary.jpg")
    
    # 步骤3: 形态学操作
    print("\n🔧 步骤3: 形态学操作")
    
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (11, 11))
    
    # 闭运算
    closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=6)
    
    # 开运算
    cleaned = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel, iterations=1)
    
    cv2.imwrite("debug_step3_cleaned.jpg", cleaned)
    print("  ✅ 形态学操作完成，保存到 debug_step3_cleaned.jpg")
    
    # 步骤4: 轮廓检测
    print("\n🔧 步骤4: 轮廓检测")
    
    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"  找到 {len(contours)} 个原始轮廓")
    
    # 极宽松的过滤条件
    filtered_contours = []
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else float('inf')
        extent = area / (w * h) if w * h > 0 else 0
        
        # 极宽松的条件
        area_ok = 500 <= area <= 2000000
        size_ok = (20 <= w <= 2000 and 15 <= h <= 1500)
        aspect_ok = 0.1 <= aspect_ratio <= 10.0
        extent_ok = extent >= 0.1
        
        print(f"    轮廓 {i+1}: 面积={area:.0f}, 尺寸={w}x{h}, 纵横比={aspect_ratio:.2f}, 填充度={extent:.2f}")
        
        if all([area_ok, size_ok, aspect_ok, extent_ok]):
            filtered_contours.append(contour)
            print(f"      ✅ 通过过滤")
        else:
            print(f"      ❌ 未通过过滤:")
            if not area_ok:
                print(f"        面积: {area:.0f} 不在 [500, 2000000]")
            if not size_ok:
                print(f"        尺寸: {w}x{h} 不在 [20-2000] x [15-1500]")
            if not aspect_ok:
                print(f"        纵横比: {aspect_ratio:.2f} 不在 [0.1, 10.0]")
            if not extent_ok:
                print(f"        填充度: {extent:.2f} < 0.1")
    
    print(f"\n🎯 最终结果: 检测到 {len(filtered_contours)} 个种子")
    
    # 创建结果图像
    result_image = image.copy()
    
    if len(filtered_contours) > 0:
        # 绘制检测到的种子
        cv2.drawContours(result_image, filtered_contours, -1, (0, 255, 0), 4)
        for i, contour in enumerate(filtered_contours):
            x, y, w, h = cv2.boundingRect(contour)
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 3)
            cv2.putText(result_image, f"Seed {i+1}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
        
        cv2.putText(result_image, f"Seeds found: {len(filtered_contours)}", 
                   (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
    else:
        # 显示所有原始轮廓用于调试
        if len(contours) > 0:
            cv2.drawContours(result_image, contours, -1, (0, 0, 255), 2)
            cv2.putText(result_image, f"Raw contours: {len(contours)}", 
                       (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        cv2.putText(result_image, "No seeds detected", 
                   (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 255), 3)
    
    cv2.imwrite("debug_final_result.jpg", result_image)
    print("  ✅ 结果图像保存到 debug_final_result.jpg")
    
    return len(filtered_contours) > 0

if __name__ == "__main__":
    try:
        success = debug_single_image()
        if success:
            print("\n🎉 调试成功! 检测到种子!")
        else:
            print("\n❌ 调试完成，但仍未检测到种子")
            print("请检查生成的调试图像来分析问题")
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
