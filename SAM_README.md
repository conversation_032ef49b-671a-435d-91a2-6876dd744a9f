# SAM Seed Segmentation Tool
# SAM种子分割工具

A high-precision seed segmentation and cropping tool based on Meta's Segment Anything Model (SAM) with a user-friendly GUI interface.

基于Meta的Segment Anything Model (SAM) 的高精度种子分割和裁剪工具，提供用户友好的图形界面。

## 🌟 Features / 功能特点

- **High-precision segmentation** - Pixel-level accurate seed boundaries / 像素级精确的种子边界
- **User-friendly GUI** - Intuitive graphical interface / 直观的图形界面  
- **Real-time progress monitoring** - Live processing updates / 实时处理进度监控
- **Preset configurations** - Quick parameter setup / 预设配置快速设置
- **Batch processing** - Handle multiple images efficiently / 高效批量处理
- **YOLO annotations** - Generate training-ready annotations / 生成训练用注释
- **Multi-language support** - Chinese and English interface / 中英文界面

## 📋 System Requirements / 系统要求

- **Python:** 3.8 or higher / 3.8或更高版本
- **Memory:** 8GB RAM minimum (16GB+ recommended) / 最少8GB内存（推荐16GB+）
- **Storage:** 5GB available space / 5GB可用存储空间
- **GPU:** Optional, CUDA-compatible GPU for acceleration / 可选，CUDA兼容GPU用于加速

## 🚀 Quick Start / 快速开始

### 1. Install Dependencies / 安装依赖
```bash
pip install segment-anything opencv-python numpy matplotlib pillow torch torchvision
```

### 2. Download SAM Model / 下载SAM模型
Download `sam_vit_h_4b8939.pth` (~2.4GB) and place it in the project directory:
下载 `sam_vit_h_4b8939.pth` (~2.4GB) 并放置在项目目录中：

**Official Link / 官方链接:** https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

### 3. Launch GUI / 启动GUI
```bash
# Recommended: Use launcher with environment checks
# 推荐：使用带环境检查的启动器
python launch_sam_gui.py

# Or launch enhanced GUI directly  
# 或直接启动增强版GUI
python sam_gui_enhanced.py

# Windows users can double-click
# Windows用户可以双击
launch_sam_gui.bat
```

## 📁 Project Structure / 项目结构

```
SAM-Seed-Segmentation/
├── sam_gui_enhanced.py          # Enhanced GUI application / 增强版GUI应用
├── sam_gui.py                   # Basic GUI application / 基础GUI应用  
├── launch_sam_gui.py            # GUI launcher with checks / 带检查的GUI启动器
├── launch_sam_gui.bat           # Windows batch launcher / Windows批处理启动器
├── sam_seed_segmenter.py        # Core processing engine / 核心处理引擎
├── sam_gui_config.json          # GUI configuration file / GUI配置文件
├── requirements_sam.txt         # Python dependencies / Python依赖
├── seed_classifier.py           # Legacy OpenCV classifier / 传统OpenCV分类器
├── CVH-seed-pic/               # Sample seed images / 示例种子图像
├── sam_vit_h_4b8939.pth        # SAM model file (download required) / SAM模型文件（需下载）
└── Documentation/              # User guides and documentation / 用户指南和文档
    ├── SAM_GUI_使用说明.md      # Detailed GUI user guide / 详细GUI使用指南
    ├── SAM_GUI_项目总结.md      # Project summary / 项目总结
    ├── SAM_使用说明.md          # SAM usage guide / SAM使用指南
    └── SAM_完整解决方案.md      # Complete solution guide / 完整解决方案指南
```

## 🖥️ GUI Interface / 图形界面

### Main Features / 主要功能:
- **File Settings Tab** - Configure input/output directories and model path / 文件设置标签页
- **Parameters Tab** - Adjust SAM and filtering parameters / 参数设置标签页
- **Processing Control Tab** - Monitor progress and control execution / 处理控制标签页
- **Preview Tab** - View results and statistics / 预览标签页

### Preset Configurations / 预设配置:
- **High Precision Mode** - Best quality, slower processing / 高精度模式
- **Balanced Mode** - Good balance of speed and quality / 平衡模式
- **Fast Mode** - Quick processing, lower precision / 快速模式
- **Large Seeds Mode** - Optimized for large seeds / 大种子模式
- **Small Seeds Mode** - Optimized for small seeds / 小种子模式

## 📊 Output Structure / 输出结构

```
output_directory/
├── crops/                       # Segmented seed images / 分割的种子图像
│   ├── species_0000012/        # Organized by species / 按物种组织
│   │   ├── S0000012-1_seed_000.jpg
│   │   └── S0000012-1_seed_001.jpg
│   └── species_0000013/
├── annotations/                 # YOLO format annotations / YOLO格式注释
│   ├── S0000012-1.txt
│   └── S0000013-1.txt
├── debug/                      # Visualization images / 可视化图像
│   ├── sam_debug_S0000012-1.jpg
│   └── sam_debug_S0000013-1.jpg
└── sam_processing_report.json  # Detailed processing report / 详细处理报告
```

## 🔧 Troubleshooting / 故障排除

### Common Issues / 常见问题:

1. **Model file missing / 模型文件缺失**
   - Download sam_vit_h_4b8939.pth from the official link
   - 从官方链接下载 sam_vit_h_4b8939.pth

2. **Memory errors / 内存错误**
   - Use CPU mode instead of GPU
   - 使用CPU模式而非GPU模式
   - Enable preview mode to process fewer images
   - 启用预览模式处理更少图像

3. **Slow processing / 处理速度慢**
   - Use GPU if available (device="cuda")
   - 如果可用，使用GPU (device="cuda")
   - Lower the points_per_side parameter
   - 降低 points_per_side 参数

4. **Poor segmentation quality / 分割质量差**
   - Adjust seed filtering parameters
   - 调整种子过滤参数
   - Use high precision preset
   - 使用高精度预设

## 📚 Documentation / 文档

- **SAM_GUI_使用说明.md** - Comprehensive GUI user guide / 完整GUI使用指南
- **SAM_GUI_项目总结.md** - Project development summary / 项目开发总结
- **SAM_使用说明.md** - SAM model usage guide / SAM模型使用指南
- **SAM_完整解决方案.md** - Complete solution overview / 完整解决方案概述

## 🤝 Support / 支持

For issues, questions, or contributions:
如有问题、疑问或贡献：

- Check the documentation files in the project directory
- 查看项目目录中的文档文件
- Review the troubleshooting section above
- 查看上述故障排除部分
- Ensure all dependencies are properly installed
- 确保所有依赖项已正确安装

## 📄 License / 许可证

This tool is based on open source components and is intended for research and educational purposes.
本工具基于开源组件，用于研究和教育目的。

---

**Thank you for using SAM Seed Segmentation Tool! / 感谢使用SAM种子分割工具！** 🌱✨
