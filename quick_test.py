#!/usr/bin/env python3
"""
快速测试改进的种子检测参数
Quick test for improved seed detection parameters
"""

import cv2
import numpy as np
from pathlib import Path

def test_seed_detection():
    """测试种子检测"""
    print("🔍 快速种子检测测试")
    print("🔍 Quick seed detection test")
    
    # 测试图像路径
    image_path = "CVH-seed-pic/S0000012-1.jpg"
    
    if not Path(image_path).exists():
        print(f"❌ 图像不存在: {image_path}")
        print(f"❌ Image not found: {image_path}")
        return
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print("❌ 无法加载图像")
        print("❌ Cannot load image")
        return
    
    print(f"✓ 图像尺寸: {image.shape[1]}x{image.shape[0]}")
    print(f"✓ Image size: {image.shape[1]}x{image.shape[0]}")
    
    # 改进的预处理
    print("\n🔧 步骤1: 预处理")
    print("🔧 Step 1: Preprocessing")
    
    # LAB颜色空间转换
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l_channel = lab[:, :, 0]
    
    # HSV颜色空间转换
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    v_channel = hsv[:, :, 2]
    
    # 结合两个通道
    gray = cv2.addWeighted(l_channel, 0.7, v_channel, 0.3, 0)
    
    # 对比度增强
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(gray)
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(enhanced, (9, 9), 3.0)
    
    cv2.imwrite("test_step1_preprocessed.jpg", blurred)
    print("  ✓ 预处理完成，保存到 test_step1_preprocessed.jpg")
    print("  ✓ Preprocessing done, saved to test_step1_preprocessed.jpg")
    
    # 改进的阈值处理
    print("\n🔧 步骤2: 阈值处理")
    print("🔧 Step 2: Thresholding")
    
    # Otsu阈值
    _, binary = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 检查前景比例
    foreground_ratio = np.sum(binary == 255) / binary.size
    print(f"  前景像素比例: {foreground_ratio:.3f}")
    print(f"  Foreground pixel ratio: {foreground_ratio:.3f}")
    
    # 如果前景太少，尝试反向阈值
    if foreground_ratio < 0.1:
        print("  前景太少，尝试反向阈值")
        print("  Too few foreground pixels, trying inverse threshold")
        _, binary = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        foreground_ratio = np.sum(binary == 255) / binary.size
        print(f"  反向阈值后前景比例: {foreground_ratio:.3f}")
        print(f"  Foreground ratio after inverse: {foreground_ratio:.3f}")
    
    cv2.imwrite("test_step2_binary.jpg", binary)
    print("  ✓ 阈值处理完成，保存到 test_step2_binary.jpg")
    print("  ✓ Thresholding done, saved to test_step2_binary.jpg")
    
    # 形态学操作
    print("\n🔧 步骤3: 形态学操作")
    print("🔧 Step 3: Morphological operations")
    
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    
    # 闭运算
    closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=4)
    
    # 开运算
    cleaned = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel, iterations=1)
    
    cv2.imwrite("test_step3_cleaned.jpg", cleaned)
    print("  ✓ 形态学操作完成，保存到 test_step3_cleaned.jpg")
    print("  ✓ Morphological operations done, saved to test_step3_cleaned.jpg")
    
    # 轮廓检测和过滤
    print("\n🔧 步骤4: 轮廓检测")
    print("🔧 Step 4: Contour detection")
    
    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"  找到 {len(contours)} 个轮廓")
    print(f"  Found {len(contours)} contours")
    
    # 改进的过滤条件
    filtered_contours = []
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else float('inf')
        extent = area / (w * h) if w * h > 0 else 0
        
        # 改进的过滤条件
        area_ok = 2000 <= area <= 500000  # 适应大种子
        size_ok = (80 <= w <= 1200 and 50 <= h <= 800)  # 适应大种子尺寸
        aspect_ok = 0.4 <= aspect_ratio <= 3.5  # 适应椭圆形种子
        extent_ok = extent >= 0.5  # 确保是实心对象
        
        print(f"    轮廓 {i+1}: 面积={area:.0f}, 尺寸={w}x{h}, 纵横比={aspect_ratio:.2f}, 填充度={extent:.2f}")
        print(f"    Contour {i+1}: area={area:.0f}, size={w}x{h}, aspect={aspect_ratio:.2f}, extent={extent:.2f}")
        
        if all([area_ok, size_ok, aspect_ok, extent_ok]):
            filtered_contours.append(contour)
            print(f"      ✅ 通过过滤")
            print(f"      ✅ Passed filtering")
        else:
            print(f"      ❌ 未通过过滤:")
            print(f"      ❌ Failed filtering:")
            if not area_ok:
                print(f"        面积: {area:.0f} 不在 [2000, 500000]")
                print(f"        Area: {area:.0f} not in [2000, 500000]")
            if not size_ok:
                print(f"        尺寸: {w}x{h} 不在 [80-1200] x [50-800]")
                print(f"        Size: {w}x{h} not in [80-1200] x [50-800]")
            if not aspect_ok:
                print(f"        纵横比: {aspect_ratio:.2f} 不在 [0.4, 3.5]")
                print(f"        Aspect: {aspect_ratio:.2f} not in [0.4, 3.5]")
            if not extent_ok:
                print(f"        填充度: {extent:.2f} < 0.5")
                print(f"        Extent: {extent:.2f} < 0.5")
    
    print(f"\n🎯 最终结果: 检测到 {len(filtered_contours)} 个种子")
    print(f"🎯 Final result: Detected {len(filtered_contours)} seeds")
    
    # 创建结果图像
    result_image = image.copy()
    if len(filtered_contours) > 0:
        cv2.drawContours(result_image, filtered_contours, -1, (0, 255, 0), 3)
        for i, contour in enumerate(filtered_contours):
            x, y, w, h = cv2.boundingRect(contour)
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 2)
            cv2.putText(result_image, f"Seed {i+1}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
    
    cv2.imwrite("test_final_result.jpg", result_image)
    print("  ✓ 结果图像保存到 test_final_result.jpg")
    print("  ✓ Result image saved to test_final_result.jpg")
    
    if len(filtered_contours) > 0:
        print("\n✅ 种子检测成功! 改进的参数有效!")
        print("✅ Seed detection successful! Improved parameters work!")
    else:
        print("\n❌ 仍未检测到种子，需要进一步调整参数")
        print("❌ Still no seeds detected, need further parameter adjustment")
    
    return len(filtered_contours) > 0

if __name__ == "__main__":
    test_seed_detection()
