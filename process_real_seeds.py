#!/usr/bin/env python3
"""
Real seed processing script that generates actual cropped seed images.
This script will process real images from the CVH dataset and create physical crop files.
"""

import os
import cv2
import numpy as np
import json
from pathlib import Path
import re
from datetime import datetime

def create_output_structure():
    """Create the output directory structure."""
    output_dir = Path("output")
    
    # Remove existing output if it exists
    if output_dir.exists():
        import shutil
        shutil.rmtree(output_dir)
    
    # Create fresh directories
    dirs = [
        output_dir,
        output_dir / "crops",
        output_dir / "annotations",
        output_dir / "debug"
    ]
    
    for dir_path in dirs:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    return output_dir

def extract_species_id(filename):
    """Extract species ID from filename using pattern S(\d+)-"""
    match = re.search(r'S(\d+)-', filename)
    return match.group(1) if match else "unknown"

def segment_seeds(image):
    """Segment seeds from image using computer vision techniques."""
    print("    Applying image preprocessing...")
    
    # Convert to grayscale
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 1.0)
    
    # Apply adaptive thresholding
    print("    Applying adaptive thresholding...")
    binary = cv2.adaptiveThreshold(
        blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY, 11, 2
    )
    
    # Morphological operations to clean up the binary image
    print("    Applying morphological operations...")
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    
    # Close small gaps
    cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
    # Remove small noise
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # Find contours
    print("    Finding contours...")
    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours based on size and shape
    print(f"    Found {len(contours)} initial contours, filtering...")
    filtered_contours = []
    
    for contour in contours:
        # Calculate contour properties
        area = cv2.contourArea(contour)
        
        # Filter by area
        if area < 300 or area > 50000:
            continue
        
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        
        # Filter by size
        if w < 15 or h < 15 or w > 500 or h > 500:
            continue
        
        # Filter by aspect ratio
        aspect_ratio = max(w, h) / min(w, h)
        if aspect_ratio > 5.0:
            continue
        
        # Filter by extent (how much of bounding box is filled)
        extent = area / (w * h)
        if extent < 0.3:
            continue
        
        filtered_contours.append(contour)
    
    print(f"    Filtered to {len(filtered_contours)} valid seed contours")
    return filtered_contours, cleaned

def extract_and_save_crops(image, contours, output_dir, species_id, base_filename):
    """Extract individual seed crops and save them as separate images."""
    species_dir = output_dir / "crops" / f"species_{species_id}"
    species_dir.mkdir(parents=True, exist_ok=True)
    
    saved_crops = []
    bounding_boxes = []
    
    print(f"    Extracting {len(contours)} seed crops...")
    
    for i, contour in enumerate(contours):
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        bounding_boxes.append((x, y, w, h))
        
        # Add padding around the seed
        padding = 10
        x_start = max(0, x - padding)
        y_start = max(0, y - padding)
        x_end = min(image.shape[1], x + w + padding)
        y_end = min(image.shape[0], y + h + padding)
        
        # Extract the crop
        crop = image[y_start:y_end, x_start:x_end]
        
        # Generate filename
        crop_filename = f"{base_filename}_seed_{i:03d}.jpg"
        crop_path = species_dir / crop_filename
        
        # Save the crop
        success = cv2.imwrite(str(crop_path), crop)
        if success:
            saved_crops.append(str(crop_path))
            print(f"      Saved: {crop_filename} ({crop.shape[1]}x{crop.shape[0]} pixels)")
        else:
            print(f"      Failed to save: {crop_filename}")
    
    return saved_crops, bounding_boxes

def create_yolo_annotation(image_shape, bounding_boxes, class_id):
    """Create YOLO format annotation."""
    height, width = image_shape
    annotations = []
    
    for x, y, w, h in bounding_boxes:
        # Convert to YOLO format (normalized center coordinates)
        center_x = (x + w / 2) / width
        center_y = (y + h / 2) / height
        norm_width = w / width
        norm_height = h / height
        
        annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}")
    
    return "\n".join(annotations)

def create_debug_image(image, contours, bounding_boxes):
    """Create debug visualization image."""
    debug_img = image.copy()
    
    # Draw contours in green
    cv2.drawContours(debug_img, contours, -1, (0, 255, 0), 2)
    
    # Draw bounding boxes in blue and add numbers
    for i, (x, y, w, h) in enumerate(bounding_boxes):
        cv2.rectangle(debug_img, (x, y), (x + w, y + h), (255, 0, 0), 2)
        # Add seed number
        cv2.putText(debug_img, f"{i}", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    return debug_img

def process_image(image_path, output_dir, species_class_map):
    """Process a single image and extract seeds."""
    print(f"\n{'='*60}")
    print(f"Processing: {os.path.basename(image_path)}")
    print(f"{'='*60}")
    
    try:
        # Load image
        print("  Loading image...")
        image = cv2.imread(str(image_path))
        if image is None:
            print("  ❌ Failed to load image")
            return None
        
        print(f"  ✅ Image loaded: {image.shape[1]}x{image.shape[0]} pixels")
        
        filename = os.path.basename(image_path)
        base_filename = Path(filename).stem
        
        # Extract species ID
        species_id = extract_species_id(filename)
        print(f"  Species ID: {species_id}")
        
        # Get or create class ID
        if species_id not in species_class_map:
            species_class_map[species_id] = len(species_class_map)
        class_id = species_class_map[species_id]
        print(f"  Class ID: {class_id}")
        
        # Segment seeds
        print("  Segmenting seeds...")
        contours, binary_img = segment_seeds(image)
        
        if len(contours) == 0:
            print("  ⚠️  No seeds detected in this image")
            return None
        
        print(f"  ✅ Found {len(contours)} seeds")
        
        # Extract and save crops
        print("  Extracting seed crops...")
        saved_crops, bounding_boxes = extract_and_save_crops(
            image, contours, output_dir, species_id, base_filename
        )
        
        # Create YOLO annotation
        print("  Creating YOLO annotation...")
        yolo_annotation = create_yolo_annotation(
            (image.shape[0], image.shape[1]), bounding_boxes, class_id
        )
        
        # Save annotation file
        annotation_file = output_dir / "annotations" / f"{base_filename}.txt"
        with open(annotation_file, 'w') as f:
            f.write(yolo_annotation)
        print(f"  ✅ Saved annotation: {annotation_file.name}")
        
        # Create and save debug image
        print("  Creating debug visualization...")
        debug_img = create_debug_image(image, contours, bounding_boxes)
        debug_path = output_dir / "debug" / f"debug_{filename}"
        cv2.imwrite(str(debug_path), debug_img)
        print(f"  ✅ Saved debug image: {debug_path.name}")
        
        result = {
            'filename': filename,
            'species_id': species_id,
            'class_id': class_id,
            'seeds_found': len(contours),
            'saved_crops': saved_crops,
            'annotation_file': str(annotation_file),
            'debug_image': str(debug_path),
            'bounding_boxes': bounding_boxes
        }
        
        print(f"  ✅ Processing completed successfully!")
        return result
        
    except Exception as e:
        print(f"  ❌ Error processing {image_path}: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main processing function."""
    print("🌱 SEED CLASSIFICATION TOOL - REAL IMAGE PROCESSING")
    print("="*70)
    
    # Create output structure
    print("\n📁 Creating output directory structure...")
    output_dir = create_output_structure()
    print(f"✅ Output directory created: {output_dir.absolute()}")
    
    # Find input images
    input_dir = Path("CVH-seed-pic/CVH-seed-pic")
    if not input_dir.exists():
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    # Get first 3 images for testing
    image_files = list(input_dir.glob("*.jpg"))[:3]
    if not image_files:
        print("❌ No JPG files found in input directory")
        return
    
    print(f"\n📸 Found {len(image_files)} images to process:")
    for img_file in image_files:
        print(f"  • {img_file.name}")
    
    # Process images
    species_class_map = {}
    results = []
    total_seeds = 0
    
    for image_path in image_files:
        result = process_image(image_path, output_dir, species_class_map)
        if result:
            results.append(result)
            total_seeds += result['seeds_found']
    
    # Save class mapping
    print(f"\n💾 Saving class mapping...")
    class_mapping = {
        'species_to_class': species_class_map,
        'class_to_species': {v: k for k, v in species_class_map.items()},
        'total_classes': len(species_class_map),
        'created_at': datetime.now().isoformat()
    }
    
    mapping_file = output_dir / "class_mapping.json"
    with open(mapping_file, 'w') as f:
        json.dump(class_mapping, f, indent=2)
    print(f"✅ Class mapping saved: {mapping_file}")
    
    # Create processing summary
    print(f"💾 Creating processing summary...")
    summary = {
        'processing_date': datetime.now().isoformat(),
        'total_images_processed': len(results),
        'total_seeds_extracted': total_seeds,
        'average_seeds_per_image': total_seeds / len(results) if results else 0,
        'species_found': list(species_class_map.keys()),
        'results': results
    }
    
    summary_file = output_dir / "processing_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"✅ Processing summary saved: {summary_file}")
    
    # Print final results
    print(f"\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
    print("="*70)
    print(f"📊 Results Summary:")
    print(f"  • Images processed: {len(results)}")
    print(f"  • Total seeds extracted: {total_seeds}")
    print(f"  • Average seeds per image: {total_seeds / len(results) if results else 0:.1f}")
    print(f"  • Species identified: {len(species_class_map)}")
    
    if species_class_map:
        print(f"\n🏷️  Species Summary:")
        for species_id, class_id in species_class_map.items():
            species_seeds = sum(r['seeds_found'] for r in results if r['species_id'] == species_id)
            species_crops = sum(len(r['saved_crops']) for r in results if r['species_id'] == species_id)
            print(f"  • Species {species_id} (Class {class_id}): {species_seeds} seeds → {species_crops} crop files")
    
    # Verify output files
    print(f"\n📁 Output Verification:")
    crop_files = list((output_dir / "crops").rglob("*.jpg"))
    annotation_files = list((output_dir / "annotations").glob("*.txt"))
    debug_files = list((output_dir / "debug").glob("*.jpg"))
    
    print(f"  ✅ Crop images: {len(crop_files)} files")
    print(f"  ✅ Annotation files: {len(annotation_files)} files")
    print(f"  ✅ Debug images: {len(debug_files)} files")
    
    if crop_files:
        print(f"\n📸 Sample crop files created:")
        for crop_file in crop_files[:5]:  # Show first 5
            rel_path = crop_file.relative_to(output_dir)
            print(f"  • {rel_path}")
    
    print(f"\n🎯 All files saved to: {output_dir.absolute()}")

if __name__ == "__main__":
    main()
