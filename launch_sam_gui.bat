@echo off
setlocal enabledelayedexpansion

REM Set UTF-8 encoding for better character support
chcp 65001 >nul 2>&1

REM Set window title
title SAM Seed Segmentation Tool - GUI Launcher

REM Clear screen and show header
cls
echo.
echo ==========================================
echo     SAM Seed Segmentation Tool
echo     Advanced GUI Launcher v2.0
echo ==========================================
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher:
    echo - Download from: https://www.python.org/downloads/
    echo - Make sure to check "Add Python to PATH" during installation
    echo.
    goto :error_exit
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [OK] Python %PYTHON_VERSION% found

REM Check if required files exist
echo [2/4] Checking required files...

if not exist "launch_sam_gui.py" (
    echo [ERROR] launch_sam_gui.py not found
    goto :error_exit
)

if not exist "sam_gui.py" (
    echo [ERROR] sam_gui.py not found
    goto :error_exit
)

if not exist "sam_seed_segmenter.py" (
    echo [ERROR] sam_seed_segmenter.py not found
    goto :error_exit
)

echo [OK] Required Python files found

REM Check for SAM model file
echo [3/4] Checking SAM model file...
if exist "sam_vit_h_4b8939.pth" (
    echo [OK] SAM model file found
) else (
    echo [WARNING] SAM model file not found: sam_vit_h_4b8939.pth
    echo You can download it from:
    echo https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
    echo.
    echo Continue anyway? (Y/N)
    set /p continue="Enter choice: "
    if /i not "!continue!"=="Y" goto :error_exit
)

REM Launch the GUI
echo [4/4] Launching SAM GUI...
echo.
echo Starting Python GUI application...
echo (This may take a moment to load)
echo.

python launch_sam_gui.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo [ERROR] GUI failed to start
    echo.
    echo Common solutions:
    echo 1. Install required packages:
    echo    pip install segment-anything opencv-python numpy matplotlib pillow
    echo.
    echo 2. Check Python environment:
    echo    python -c "import tkinter; print('tkinter OK')"
    echo.
    echo 3. Run environment test:
    echo    python test_sam_setup.py
    echo.
    echo 4. Check error messages above for specific issues
    echo.
    goto :error_exit
) else (
    echo.
    echo [OK] GUI closed successfully
    echo.
    echo Thank you for using SAM Seed Segmentation Tool!
    echo.
)

goto :normal_exit

:error_exit
echo.
echo ==========================================
echo Press any key to exit...
pause >nul
exit /b 1

:normal_exit
echo Press any key to exit...
pause >nul
exit /b 0
