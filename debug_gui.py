#!/usr/bin/env python3
"""
Debug script to test GUI components step by step
"""

import sys
import traceback

def test_basic_imports():
    """Test basic imports"""
    try:
        print("Testing basic imports...")
        import tkinter as tk
        print("✅ tkinter imported")
        
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter submodules imported")
        
        import json
        print("✅ json imported")
        
        import os
        print("✅ os imported")
        
        from pathlib import Path
        print("✅ pathlib imported")
        
        from datetime import datetime
        print("✅ datetime imported")
        
        return True
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        traceback.print_exc()
        return False

def test_pil_imports():
    """Test PIL imports"""
    try:
        print("\nTesting PIL imports...")
        from PIL import Image, ImageTk
        print("✅ PIL imported")
        return True
    except Exception as e:
        print(f"❌ PIL import failed: {e}")
        print("Install with: pip install pillow")
        return False

def test_opencv_imports():
    """Test OpenCV imports"""
    try:
        print("\nTesting OpenCV imports...")
        import cv2
        print("✅ OpenCV imported")
        return True
    except Exception as e:
        print(f"❌ OpenCV import failed: {e}")
        print("Install with: pip install opencv-python")
        return False

def test_numpy_imports():
    """Test NumPy imports"""
    try:
        print("\nTesting NumPy imports...")
        import numpy as np
        print("✅ NumPy imported")
        return True
    except Exception as e:
        print(f"❌ NumPy import failed: {e}")
        print("Install with: pip install numpy")
        return False

def test_config_file():
    """Test config file"""
    try:
        print("\nTesting config file...")
        import json
        with open("sam_gui_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ Config file loaded")
        
        if 'languages' in config:
            print("✅ Languages section found")
        else:
            print("❌ Languages section missing")
            
        return True
    except Exception as e:
        print(f"❌ Config file error: {e}")
        traceback.print_exc()
        return False

def test_advanced_gui_import():
    """Test advanced GUI import"""
    try:
        print("\nTesting advanced GUI import...")
        from sam_gui_advanced import LanguageManager
        print("✅ LanguageManager imported")
        
        from sam_gui_advanced import SessionManager
        print("✅ SessionManager imported")
        
        from sam_gui_advanced import ImagePreviewWidget
        print("✅ ImagePreviewWidget imported")
        
        from sam_gui_advanced import SAMGUIAdvanced
        print("✅ SAMGUIAdvanced imported")
        
        return True
    except Exception as e:
        print(f"❌ Advanced GUI import failed: {e}")
        traceback.print_exc()
        return False

def test_simple_gui():
    """Test simple GUI creation"""
    try:
        print("\nTesting simple GUI creation...")
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Test basic widget creation
        frame = tk.Frame(root)
        label = tk.Label(frame, text="Test")
        button = tk.Button(frame, text="Test Button")
        
        print("✅ Basic widgets created")
        
        root.destroy()
        return True
    except Exception as e:
        print(f"❌ Simple GUI creation failed: {e}")
        traceback.print_exc()
        return False

def test_language_manager():
    """Test language manager"""
    try:
        print("\nTesting language manager...")
        from sam_gui_advanced import LanguageManager
        
        lang_mgr = LanguageManager("sam_gui_config.json")
        print("✅ LanguageManager created")
        
        # Test language switching
        lang_mgr.set_language('en')
        print(f"✅ Language set to: {lang_mgr.current_language}")
        
        # Test text retrieval
        text = lang_mgr.get_text('app_title', 'Default')
        print(f"✅ Text retrieved: {text}")
        
        return True
    except Exception as e:
        print(f"❌ Language manager test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("SAM Advanced GUI Debug Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("PIL Imports", test_pil_imports),
        ("OpenCV Imports", test_opencv_imports),
        ("NumPy Imports", test_numpy_imports),
        ("Config File", test_config_file),
        ("Advanced GUI Import", test_advanced_gui_import),
        ("Simple GUI", test_simple_gui),
        ("Language Manager", test_language_manager),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            traceback.print_exc()
        print("-" * 30)
    
    print(f"\nResults: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Try launching the GUI now.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
