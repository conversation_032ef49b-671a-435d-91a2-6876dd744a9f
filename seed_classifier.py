#!/usr/bin/env python3
"""
Seed Classification Tool
========================

A comprehensive Python tool for automatic seed segmentation, classification, 
and YOLO annotation generation from seed images.

Author: Augment Agent
Date: 2025-06-19
"""

import os
import cv2
import numpy as np
import json
import logging
import argparse
from pathlib import Path
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass, asdict
import re
from datetime import datetime
import shutil


@dataclass
class SegmentationConfig:
    """Configuration parameters for seed segmentation."""
    # Preprocessing parameters
    gaussian_blur_kernel: int = 7  # 增大模糊核以减少纹理干扰
    gaussian_blur_sigma: float = 2.0  # 增大sigma值

    # Thresholding parameters
    threshold_method: str = "otsu"  # 改用Otsu阈值，对颜色对比度好的图像效果更好
    manual_threshold: int = 127
    adaptive_method: int = cv2.ADAPTIVE_THRESH_GAUSSIAN_C
    adaptive_type: int = cv2.THRESH_BINARY
    adaptive_block_size: int = 15  # 增大块大小
    adaptive_c: int = 5  # 增大C值

    # Morphological operations
    morph_kernel_size: int = 5  # 增大形态学核
    morph_iterations: int = 3  # 增加迭代次数

    # Contour filtering - 放宽条件以适应大种子
    min_contour_area: int = 1000  # 增大最小面积，适应较大种子
    max_contour_area: int = 200000  # 大幅增大最大面积
    min_aspect_ratio: float = 0.3  # 稍微放宽纵横比
    max_aspect_ratio: float = 4.0  # 适应椭圆形种子
    min_extent: float = 0.4  # 提高extent要求，确保是实心对象

    # Seed quality filtering - 适应大种子
    min_seed_width: int = 50  # 大幅增加最小宽度
    min_seed_height: int = 30  # 增加最小高度
    max_seed_width: int = 800  # 增大最大宽度
    max_seed_height: int = 600  # 增大最大高度


@dataclass
class ProcessingConfig:
    """Configuration for processing pipeline."""
    input_directory: str
    output_directory: str
    preview_mode: bool = False
    max_preview_images: int = 5
    save_debug_images: bool = False
    create_yolo_annotations: bool = True
    organize_by_species: bool = True
    species_extraction_pattern: str = r"S(\d+)-"  # Extract species ID from filename
    
    # Image formats to process
    supported_formats: List[str] = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']


class SeedProcessor:
    """Main class for processing seed images."""
    
    def __init__(self, seg_config: SegmentationConfig, proc_config: ProcessingConfig):
        self.seg_config = seg_config
        self.proc_config = proc_config
        self.logger = self._setup_logging()
        self.species_class_map = {}  # Maps species ID to class index
        self.processing_stats = {
            'total_images': 0,
            'processed_images': 0,
            'total_seeds_found': 0,
            'failed_images': 0,
            'species_counts': {}
        }
        
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger('SeedProcessor')
        logger.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler
        log_file = Path(self.proc_config.output_directory) / 'processing.log'
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def extract_species_from_filename(self, filename: str) -> Optional[str]:
        """Extract species identifier from filename."""
        match = re.search(self.proc_config.species_extraction_pattern, filename)
        if match:
            return match.group(1)
        return None
    
    def get_species_class_id(self, species_id: str) -> int:
        """Get or create class ID for species."""
        if species_id not in self.species_class_map:
            self.species_class_map[species_id] = len(self.species_class_map)
        return self.species_class_map[species_id]
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for segmentation.
        Enhanced for colored seeds on colored backgrounds.
        """
        # 对于彩色种子图像，使用更好的颜色空间转换
        if len(image.shape) == 3:
            # 尝试LAB颜色空间的L通道，通常对种子分割效果更好
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l_channel = lab[:, :, 0]  # L通道包含亮度信息

            # 也尝试HSV的V通道
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            v_channel = hsv[:, :, 2]

            # 结合两个通道的信息，L通道权重更高
            gray = cv2.addWeighted(l_channel, 0.7, v_channel, 0.3, 0)
        else:
            gray = image.copy()

        # 应用对比度增强
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)

        # 应用更强的高斯模糊以减少种子表面纹理的干扰
        blurred = cv2.GaussianBlur(
            enhanced,
            (self.seg_config.gaussian_blur_kernel, self.seg_config.gaussian_blur_kernel),
            self.seg_config.gaussian_blur_sigma
        )

        return blurred
    
    def threshold_image(self, image: np.ndarray) -> np.ndarray:
        """Apply thresholding to create binary image."""
        if self.seg_config.threshold_method == "adaptive":
            binary = cv2.adaptiveThreshold(
                image,
                255,
                self.seg_config.adaptive_method,
                self.seg_config.adaptive_type,
                self.seg_config.adaptive_block_size,
                self.seg_config.adaptive_c
            )
        elif self.seg_config.threshold_method == "otsu":
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        else:  # manual
            _, binary = cv2.threshold(
                image, 
                self.seg_config.manual_threshold, 
                255, 
                cv2.THRESH_BINARY
            )
        
        return binary
    
    def apply_morphological_operations(self, binary: np.ndarray) -> np.ndarray:
        """Apply morphological operations to clean up binary image."""
        kernel = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE,
            (self.seg_config.morph_kernel_size, self.seg_config.morph_kernel_size)
        )
        
        # Close small gaps
        closed = cv2.morphologyEx(
            binary, 
            cv2.MORPH_CLOSE, 
            kernel, 
            iterations=self.seg_config.morph_iterations
        )
        
        # Remove small noise
        opened = cv2.morphologyEx(
            closed, 
            cv2.MORPH_OPEN, 
            kernel, 
            iterations=1
        )
        
        return opened
    
    def find_and_filter_contours(self, binary: np.ndarray) -> List[np.ndarray]:
        """Find and filter contours based on size and shape criteria."""
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        filtered_contours = []
        for contour in contours:
            # Calculate contour properties
            area = cv2.contourArea(contour)
            if area < self.seg_config.min_contour_area or area > self.seg_config.max_contour_area:
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check size constraints
            if (w < self.seg_config.min_seed_width or w > self.seg_config.max_seed_width or
                h < self.seg_config.min_seed_height or h > self.seg_config.max_seed_height):
                continue
            
            # Check aspect ratio
            aspect_ratio = max(w, h) / min(w, h)
            if (aspect_ratio < self.seg_config.min_aspect_ratio or 
                aspect_ratio > self.seg_config.max_aspect_ratio):
                continue
            
            # Check extent (how much of the bounding rectangle is filled)
            extent = area / (w * h)
            if extent < self.seg_config.min_extent:
                continue
            
            filtered_contours.append(contour)
        
        return filtered_contours
    
    def segment_seeds(self, image: np.ndarray) -> Tuple[List[np.ndarray], np.ndarray]:
        """
        Segment seeds from image.

        Returns:
            Tuple of (contours, debug_image)
        """
        # Preprocess
        preprocessed = self.preprocess_image(image)

        # Threshold
        binary = self.threshold_image(preprocessed)

        # Morphological operations
        cleaned = self.apply_morphological_operations(binary)

        # Find and filter contours
        contours = self.find_and_filter_contours(cleaned)

        # Create debug image if needed
        debug_image = None
        if self.proc_config.save_debug_images:
            debug_image = image.copy()
            cv2.drawContours(debug_image, contours, -1, (0, 255, 0), 2)

            # Draw bounding rectangles
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(debug_image, (x, y), (x + w, y + h), (255, 0, 0), 1)

        return contours, debug_image

    def extract_seed_crops(self, image: np.ndarray, contours: List[np.ndarray]) -> List[Tuple[np.ndarray, Tuple[int, int, int, int]]]:
        """
        Extract individual seed crops from image using contours.

        Returns:
            List of (cropped_image, bounding_box) tuples
        """
        crops = []
        for contour in contours:
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)

            # Add padding
            padding = 5
            x_pad = max(0, x - padding)
            y_pad = max(0, y - padding)
            w_pad = min(image.shape[1] - x_pad, w + 2 * padding)
            h_pad = min(image.shape[0] - y_pad, h + 2 * padding)

            # Extract crop
            crop = image[y_pad:y_pad + h_pad, x_pad:x_pad + w_pad]

            # Store original bounding box (without padding) for YOLO annotations
            crops.append((crop, (x, y, w, h)))

        return crops

    def create_yolo_annotation(self, image_shape: Tuple[int, int], bounding_boxes: List[Tuple[int, int, int, int]], class_id: int) -> str:
        """
        Create YOLO format annotation string.

        Args:
            image_shape: (height, width) of the original image
            bounding_boxes: List of (x, y, w, h) bounding boxes
            class_id: Class ID for the species

        Returns:
            YOLO format annotation string
        """
        height, width = image_shape
        annotations = []

        for x, y, w, h in bounding_boxes:
            # Convert to YOLO format (normalized center coordinates and dimensions)
            center_x = (x + w / 2) / width
            center_y = (y + h / 2) / height
            norm_width = w / width
            norm_height = h / height

            annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}")

        return "\n".join(annotations)

    def save_seed_crops(self, crops: List[Tuple[np.ndarray, Tuple[int, int, int, int]]],
                       species_id: str, original_filename: str) -> List[str]:
        """
        Save individual seed crops to species-specific directories.

        Returns:
            List of saved filenames
        """
        species_dir = Path(self.proc_config.output_directory) / "crops" / f"species_{species_id}"
        species_dir.mkdir(parents=True, exist_ok=True)

        saved_files = []
        base_name = Path(original_filename).stem

        for i, (crop, bbox) in enumerate(crops):
            crop_filename = f"{base_name}_seed_{i:03d}.jpg"
            crop_path = species_dir / crop_filename

            # Save crop
            cv2.imwrite(str(crop_path), crop)
            saved_files.append(str(crop_path))

        return saved_files

    def process_single_image(self, image_path: str) -> Dict:
        """
        Process a single image and extract seeds.

        Returns:
            Dictionary with processing results
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")

            filename = os.path.basename(image_path)
            self.logger.info(f"Processing {filename}")

            # Extract species ID
            species_id = self.extract_species_from_filename(filename)
            if species_id is None:
                self.logger.warning(f"Could not extract species ID from {filename}")
                species_id = "unknown"

            # Segment seeds
            contours, debug_image = self.segment_seeds(image)

            if len(contours) == 0:
                self.logger.warning(f"No seeds found in {filename}")
                return {
                    'filename': filename,
                    'species_id': species_id,
                    'seeds_found': 0,
                    'success': False,
                    'error': 'No seeds detected'
                }

            # Extract seed crops
            crops = self.extract_seed_crops(image, contours)

            # Save crops if not in preview mode
            saved_files = []
            if not self.proc_config.preview_mode:
                saved_files = self.save_seed_crops(crops, species_id, filename)

            # Create YOLO annotations
            yolo_annotation = ""
            if self.proc_config.create_yolo_annotations:
                class_id = self.get_species_class_id(species_id)
                bounding_boxes = [bbox for _, bbox in crops]
                yolo_annotation = self.create_yolo_annotation(
                    (image.shape[0], image.shape[1]),
                    bounding_boxes,
                    class_id
                )

                # Save annotation file if not in preview mode
                if not self.proc_config.preview_mode:
                    annotation_dir = Path(self.proc_config.output_directory) / "annotations"
                    annotation_dir.mkdir(parents=True, exist_ok=True)
                    annotation_file = annotation_dir / f"{Path(filename).stem}.txt"
                    with open(annotation_file, 'w') as f:
                        f.write(yolo_annotation)

            # Save debug image if requested
            if debug_image is not None and not self.proc_config.preview_mode:
                debug_dir = Path(self.proc_config.output_directory) / "debug"
                debug_dir.mkdir(parents=True, exist_ok=True)
                debug_path = debug_dir / f"debug_{filename}"
                cv2.imwrite(str(debug_path), debug_image)

            # Update statistics
            self.processing_stats['total_seeds_found'] += len(contours)
            if species_id not in self.processing_stats['species_counts']:
                self.processing_stats['species_counts'][species_id] = 0
            self.processing_stats['species_counts'][species_id] += len(contours)

            return {
                'filename': filename,
                'species_id': species_id,
                'seeds_found': len(contours),
                'success': True,
                'saved_files': saved_files,
                'yolo_annotation': yolo_annotation,
                'debug_image_path': str(debug_dir / f"debug_{filename}") if debug_image is not None else None
            }

        except Exception as e:
            self.logger.error(f"Error processing {image_path}: {str(e)}")
            return {
                'filename': os.path.basename(image_path),
                'species_id': 'unknown',
                'seeds_found': 0,
                'success': False,
                'error': str(e)
            }


    def get_image_files(self) -> List[str]:
        """Get list of image files to process."""
        input_path = Path(self.proc_config.input_directory)
        if not input_path.exists():
            raise ValueError(f"Input directory does not exist: {input_path}")

        image_files = []
        for ext in self.proc_config.supported_formats:
            pattern = f"*{ext}"
            image_files.extend(input_path.glob(pattern))
            image_files.extend(input_path.glob(pattern.upper()))

        # Also check subdirectories
        for ext in self.proc_config.supported_formats:
            pattern = f"**/*{ext}"
            image_files.extend(input_path.glob(pattern))
            image_files.extend(input_path.glob(pattern.upper()))

        # Remove duplicates and convert to strings
        image_files = list(set(str(f) for f in image_files))
        image_files.sort()

        return image_files

    def process_batch(self) -> Dict:
        """
        Process all images in the input directory.

        Returns:
            Dictionary with batch processing results
        """
        # Get image files
        image_files = self.get_image_files()

        if not image_files:
            self.logger.error("No image files found in input directory")
            return {'success': False, 'error': 'No image files found'}

        self.processing_stats['total_images'] = len(image_files)
        self.logger.info(f"Found {len(image_files)} image files to process")

        # Limit files in preview mode
        if self.proc_config.preview_mode:
            image_files = image_files[:self.proc_config.max_preview_images]
            self.logger.info(f"Preview mode: processing only {len(image_files)} images")

        # Process each image
        results = []
        for i, image_path in enumerate(image_files, 1):
            self.logger.info(f"Processing image {i}/{len(image_files)}: {os.path.basename(image_path)}")

            result = self.process_single_image(image_path)
            results.append(result)

            if result['success']:
                self.processing_stats['processed_images'] += 1
            else:
                self.processing_stats['failed_images'] += 1

        # Save class mapping
        if not self.proc_config.preview_mode and self.species_class_map:
            self.save_class_mapping()

        # Generate summary report
        summary = self.generate_summary_report(results)

        return {
            'success': True,
            'results': results,
            'summary': summary,
            'stats': self.processing_stats
        }

    def save_class_mapping(self):
        """Save species to class ID mapping."""
        mapping_file = Path(self.proc_config.output_directory) / "class_mapping.json"

        # Create reverse mapping (class_id -> species_id)
        reverse_mapping = {v: k for k, v in self.species_class_map.items()}

        mapping_data = {
            'species_to_class': self.species_class_map,
            'class_to_species': reverse_mapping,
            'total_classes': len(self.species_class_map),
            'created_at': datetime.now().isoformat()
        }

        with open(mapping_file, 'w') as f:
            json.dump(mapping_data, f, indent=2)

        self.logger.info(f"Saved class mapping to {mapping_file}")

    def generate_summary_report(self, results: List[Dict]) -> Dict:
        """Generate summary report of processing results."""
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        summary = {
            'processing_date': datetime.now().isoformat(),
            'total_images': len(results),
            'successful_images': len(successful_results),
            'failed_images': len(failed_results),
            'total_seeds_extracted': sum(r['seeds_found'] for r in successful_results),
            'species_summary': {},
            'average_seeds_per_image': 0,
            'configuration': {
                'segmentation': asdict(self.seg_config),
                'processing': asdict(self.proc_config)
            }
        }

        # Calculate species summary
        for result in successful_results:
            species_id = result['species_id']
            if species_id not in summary['species_summary']:
                summary['species_summary'][species_id] = {
                    'images_processed': 0,
                    'total_seeds': 0,
                    'average_seeds_per_image': 0
                }

            summary['species_summary'][species_id]['images_processed'] += 1
            summary['species_summary'][species_id]['total_seeds'] += result['seeds_found']

        # Calculate averages
        for species_data in summary['species_summary'].values():
            if species_data['images_processed'] > 0:
                species_data['average_seeds_per_image'] = (
                    species_data['total_seeds'] / species_data['images_processed']
                )

        if len(successful_results) > 0:
            summary['average_seeds_per_image'] = (
                summary['total_seeds_extracted'] / len(successful_results)
            )

        # Save summary report
        if not self.proc_config.preview_mode:
            summary_file = Path(self.proc_config.output_directory) / "processing_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            self.logger.info(f"Saved summary report to {summary_file}")

        return summary


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description='Seed Classification Tool')
    parser.add_argument('input_dir', help='Input directory containing seed images')
    parser.add_argument('output_dir', help='Output directory for results')
    parser.add_argument('--preview', action='store_true', help='Preview mode - process only a few images')
    parser.add_argument('--debug', action='store_true', help='Save debug images')
    parser.add_argument('--config', help='Path to configuration JSON file')

    args = parser.parse_args()

    # Load configuration
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config_data = json.load(f)
        seg_config = SegmentationConfig(**config_data.get('segmentation', {}))
        proc_config_data = config_data.get('processing', {})
    else:
        seg_config = SegmentationConfig()
        proc_config_data = {}

    # Update processing config with command line arguments
    proc_config_data.update({
        'input_directory': args.input_dir,
        'output_directory': args.output_dir,
        'preview_mode': args.preview,
        'save_debug_images': args.debug
    })

    proc_config = ProcessingConfig(**proc_config_data)

    # Create processor and run
    processor = SeedProcessor(seg_config, proc_config)
    processor.logger.info("Starting seed processing...")

    try:
        # Process images
        batch_results = processor.process_batch()

        if batch_results['success']:
            summary = batch_results['summary']
            print("\n" + "="*60)
            print("SEED PROCESSING COMPLETED SUCCESSFULLY")
            print("="*60)
            print(f"Total images processed: {summary['successful_images']}/{summary['total_images']}")
            print(f"Total seeds extracted: {summary['total_seeds_extracted']}")
            print(f"Average seeds per image: {summary['average_seeds_per_image']:.1f}")
            print(f"Number of species found: {len(summary['species_summary'])}")

            if summary['species_summary']:
                print("\nSpecies Summary:")
                for species_id, data in summary['species_summary'].items():
                    print(f"  Species {species_id}: {data['total_seeds']} seeds from {data['images_processed']} images")

            print(f"\nResults saved to: {proc_config.output_directory}")

            if proc_config.preview_mode:
                print("\nNote: This was a preview run. Use without --preview to process all images.")
        else:
            print(f"Processing failed: {batch_results.get('error', 'Unknown error')}")

    except Exception as e:
        processor.logger.error(f"Fatal error: {str(e)}")
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
