{"segmentation": {"gaussian_blur_kernel": 11, "gaussian_blur_sigma": 4.0, "threshold_method": "otsu", "manual_threshold": 127, "adaptive_method": 1, "adaptive_type": 0, "adaptive_block_size": 25, "adaptive_c": 10, "morph_kernel_size": 9, "morph_iterations": 5, "min_contour_area": 1500, "max_contour_area": 800000, "min_aspect_ratio": 0.3, "max_aspect_ratio": 4.0, "min_extent": 0.4, "min_seed_width": 60, "min_seed_height": 40, "max_seed_width": 1500, "max_seed_height": 1000}, "processing": {"supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"], "create_yolo_annotations": true, "save_debug_images": true, "preview_mode": false, "max_preview_images": 10}, "description": "多背景颜色优化配置 - 适用于绿色、蓝色、白色、黑色、灰色背景的种子图像", "background_types_supported": ["绿色背景 (如植物标本背景)", "蓝色背景 (如天空色背景)", "白色背景 (如纸张背景)", "黑色背景 (如暗色背景)", "灰色背景 (如中性背景)"], "optimization_features": ["多通道自适应融合 (LAB-L + HSV-V + 标准灰度)", "基于对比度的动态权重调整", "智能Otsu阈值 (自动选择正向/反向)", "自适应CLAHE对比度增强", "根据背景亮度调整处理参数", "更宽松的尺寸和形状约束"], "parameter_adjustments": {"gaussian_blur_kernel": "11 - 更大的模糊核，适应各种纹理背景", "gaussian_blur_sigma": "4.0 - 更强的模糊，减少背景干扰", "threshold_method": "otsu - 智能双向Otsu，自动适应背景", "morph_kernel_size": "9 - 更大的形态学核，处理复杂背景", "morph_iterations": "5 - 更多迭代，彻底清理噪声", "min_contour_area": "1500 - 降低最小面积，避免遗漏小种子", "max_contour_area": "800000 - 大幅提高最大面积，适应超大种子", "min_seed_width": "60 - 适应中等大小种子", "min_seed_height": "40 - 适应椭圆形种子", "max_seed_width": "1500 - 允许非常大的种子", "max_seed_height": "1000 - 允许非常高的种子", "max_aspect_ratio": "4.0 - 适应长椭圆形种子", "min_extent": "0.4 - 稍微降低填充度要求，适应不规则形状"}, "usage_scenarios": {"绿色背景": "植物标本照片，种子与绿色背景对比", "蓝色背景": "天空色或蓝色卡纸背景", "白色背景": "白纸或白色背景板", "黑色背景": "黑色背景板或暗色环境", "灰色背景": "中性灰色背景或自然光照"}, "expected_improvements": ["显著提高多背景适应性", "自动选择最佳阈值方向", "减少背景颜色对检测的影响", "提高种子边界检测精度", "适应更大范围的种子尺寸", "减少误检和漏检"]}